"use client";

import { useRouter } from "next/navigation";
import AddForm from "@/components/common/AddForm";
import { organizationFields } from "@/utils/data/organizations";
import { ORGANIZATION_CONSTANTS } from "@/utils/constants/organization";
import { useDispatch } from "react-redux";
import { createOrganization } from "@/redux/Thunks/organization.js";
import { useToast } from "@/components/ui/toast";
import { extractApiErrorMessage } from "@/utils/errorHandler";

export default function AddOrganizationPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { addToast } = useToast();

  const handleSubmit = async (values) => {
    try {
      const resultAction = await dispatch(createOrganization(values));
      if (createOrganization.fulfilled.match(resultAction)) {
        addToast(
          resultAction.payload?.message,
          "success"
        );
        router.push("/masters/org");
      } else {
        // Extract error message from the error response
        const errorMessage = extractApiErrorMessage(resultAction.payload);
        addToast(errorMessage, "error");
      }
    } catch (error) {
      addToast(extractApiErrorMessage(error), "error");
    }
  };

  return (
    <AddForm
      heading={ORGANIZATION_CONSTANTS.ADD_PAGE.HEADING}
      subTitle={ORGANIZATION_CONSTANTS.ADD_PAGE.SUBTITLE}
      onBack={() => router.push("/masters/org")}
      backLabel={ORGANIZATION_CONSTANTS.ADD_PAGE.BACK_LABEL}
      title={ORGANIZATION_CONSTANTS.ADD_PAGE.TITLE}
      fields={organizationFields}
      onSubmit={handleSubmit}
      submitLabel={ORGANIZATION_CONSTANTS.ADD_PAGE.SUBMIT_LABEL}
    />
  );
}
