"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import AddForm from "@/components/common/AddForm";
import { userFields } from "@/utils/data/users";
import { USER_CONSTANTS } from "@/utils/constants/user";
import { fetchOrganizations } from "@/redux/Thunks/organization.js";
import { createUser } from "@/redux/Thunks/userThunks.js";

export default function AddUserPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { organizations, loading: orgLoading } = useSelector(
    (state) => state.organizations
  );
  const { loading: userLoading, error: userError } = useSelector(
    (state) => state.users
  );
  const [fields, setFields] = useState(userFields);

  // Fetch organizations on component mount
  useEffect(() => {
    dispatch(fetchOrganizations());
  }, [dispatch]);

  // Update fields with organization options when organizations are loaded
  useEffect(() => {
    if (organizations && organizations.length > 0) {
      const updatedFields = userFields.map((field) => {
        if (field.name === "organization_id") {
          return {
            ...field,
            options: organizations.map((org) => ({
              label: org.name || org.organization_name,
              value: org.id,
            })),
          };
        }
        return field;
      });
      setFields(updatedFields);
    }
  }, [organizations]);

  const handleSubmit = async (values) => {
    // Validate that all required fields are present
    const requiredFields = [
      "organization_id",
      "email",
      "password",
      "full_name",
      "phone_number",
      "role",
    ];
    const missingFields = requiredFields.filter((field) => !values[field]);

    if (missingFields.length > 0) {
      console.error("Missing required fields:", missingFields);
      return;
    }

    // Prepare the data for the API request
    const userData = {
      organization_id: values.organization_id,
      email: values.email,
      password: values.password,
      full_name: values.full_name,
      phone_number: values.phone_number,
      role: values.role,
    };

    try {
      // Dispatch the createUser action
      const result = await dispatch(createUser(userData));

      if (createUser.fulfilled.match(result)) {
        // Redirect to users list on success
        router.push("/masters/user");
      } else {
        console.error("Failed to create user:", result.payload);
        // Error handling is already done in the thunk
      }
    } catch (error) {
      console.error("Error creating user:", error);
    }
  };

  // Show loading state while organizations are being fetched or user is being created
  if (orgLoading || userLoading) {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {USER_CONSTANTS.ADD_PAGE.HEADING}
            </h1>
            <p className="text-gray-600">{USER_CONSTANTS.ADD_PAGE.SUBTITLE}</p>
          </div>
          <button
            onClick={() => router.push("/masters/user")}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            {USER_CONSTANTS.ADD_PAGE.BACK_LABEL}
          </button>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6C63FF] mx-auto mb-4"></div>
              <p className="text-gray-600">
                {orgLoading ? "Loading organizations..." : "Creating user..."}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Error Display */}
      {userError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error creating user
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{userError}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <AddForm
        heading={USER_CONSTANTS.ADD_PAGE.HEADING}
        subTitle={USER_CONSTANTS.ADD_PAGE.SUBTITLE}
        onBack={() => router.push("/masters/user")}
        backLabel={USER_CONSTANTS.ADD_PAGE.BACK_LABEL}
        title={USER_CONSTANTS.ADD_PAGE.TITLE}
        fields={fields}
        onSubmit={handleSubmit}
        submitLabel={USER_CONSTANTS.ADD_PAGE.SUBMIT_LABEL}
      />
    </div>
  );
}
