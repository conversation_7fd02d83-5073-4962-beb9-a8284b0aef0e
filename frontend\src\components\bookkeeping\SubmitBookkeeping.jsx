"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import Heading from "@/components/bookkeeping/Heading";
import ClientInfoCard from "@/components/bookkeeping/ClientInfo";
import MonthSelector from "@/components/bookkeeping/MonthSelector";
import UploadSection from "@/components/bookkeeping/UploadSection";
import ActionButtons from "@/components/bookkeeping/Actions";
import { clientsData } from "@/utils/data/clients";
import { bookkeepingMonths } from "@/utils/data/bookkeeping";
import { useToast } from "../ui/toast";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";
import {
  syncFinancialData,
  syncOperationalData,
} from "@/redux/Thunks/bookkeeping";

export default function SubmitBookkeeping({ clientId }) {
  const router = useRouter();
  const dispatch = useDispatch();
  const { addToast } = useToast();

  // Redux state
  const { financial, operational } = useSelector((state) => state.bookkeeping);

  const selectedClientData = clientsData.find((c) => String(c.id) === clientId);
  const [selectedMonth, setSelectedMonth] = useState(bookkeepingMonths[0]);
  const [uploadStates, setUploadStates] = useState({
    financial: false,
    operational: false,
    payroll: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDrafting, setIsDrafting] = useState(false);

  // Handlers
  const handleMonthChange = (month) => setSelectedMonth(month);



  // Separate method for ADP/Payroll sync
  const handleAdpSync = (files) => {
    // Simulate loading state for UI feedback
    setUploadStates((prev) => ({ ...prev, payroll: true }));
    setTimeout(() => {
      setUploadStates((prev) => ({ ...prev, payroll: false }));
      addToast("ADP/Payroll sync logged to console", "info");
    }, 1000);
  };

  const handleSync = async (type, files) => {
    setUploadStates((prev) => ({ ...prev, [type]: true }));

    try {
      if (type === "financial") {
        await dispatch(
          syncFinancialData({
            clientId,
            month: selectedMonth,
            files: files || [],
          })
        ).unwrap();
        addToast("Successfully synced financial data", "success");
      } else if (type === "operational") {
        await dispatch(
          syncOperationalData({
            clientId,
            month: selectedMonth,
            files: files || [],
          })
        ).unwrap();
        addToast("Successfully synced operational data", "success");
      } else if (type === "payroll") {
        // Use the separate ADP method
        handleAdpSync(files);
        return; // Early return to avoid the finally block
      }
    } catch (error) {
      console.error(`Error syncing ${type} data:`, error);
      addToast(
        `Failed to sync ${type} data: ${error.message || error}`,
        "error"
      );
    } finally {
      // Only reset loading state for financial and operational
      if (type !== "payroll") {
        setUploadStates((prev) => ({ ...prev, [type]: false }));
      }
    }
  };

  const handleSaveAsDraft = () => {
    setIsDrafting(true);
    setTimeout(() => {
      setIsDrafting(false);
      addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.DRAFT_SAVED, "success");
    }, 1500);
  };

  const handleCancel = () => {
    router.push("/listing");
  };

  const handleSubmitData = () => {
    setIsSubmitting(true);
    // Simulate submit
    setTimeout(() => {
      setIsSubmitting(false);
      addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.DATA_SUBMITTED, "success");
      router.push("/listing");
    }, 1500);
  };

  return (
    <div className="max-w-5xl mx-auto bg-gray-100">
      <Heading onBack={() => router.push("/listing")} />
      <ClientInfoCard client={selectedClientData} />
      <MonthSelector
        selectedMonth={selectedMonth}
        onChange={handleMonthChange}
        months={bookkeepingMonths}
      />


      <UploadSection
        uploadStates={{
          ...uploadStates,
          financial: financial.loading,
          operational: operational.loading,


        }}
        id={clientId}
        onSync={handleSync}
      />
      <ActionButtons
        isSubmitting={isSubmitting}
        isDrafting={isDrafting}
        onSaveAsDraft={handleSaveAsDraft}
        onCancel={handleCancel}
        onSubmit={handleSubmitData}
      />
    </div>
  );
}
