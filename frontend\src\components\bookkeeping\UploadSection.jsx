import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import PropTypes from "prop-types";
import UploadCard from "@/components/bookkeeping/UploadCard";
import { cardConfigs } from "@/utils/data/bookkeeping";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";
import { uploadAdpFile } from "@/redux/Thunks/adp.js";
import { fetchOrganizations } from "@/redux/Thunks/organization.js";
import { syncFinancialData } from "@/redux/Thunks/bookkeeping.js";
import { formatDateToDDMMYYYY } from "@/utils/methods/formatters";
import { Upload } from "lucide-react"; // Lucide icon

export default function UploadSection({ uploadStates, id }) {
  const dispatch = useDispatch();
  const [localUploadStates, setLocalUploadStates] = useState({});
  const [schemaName, setSchemaName] = useState(null);
  const [organizationServices, setOrganizationServices] = useState([]);
  const [qbLastSyncedAt, setQbLastSyncedAt] = useState(null);
  const [realmId, setRealmId] = useState(null);

  // Fetch organizations and find the one with matching ID
  useEffect(() => {
    const fetchOrgData = async () => {
      try {
        const result = await dispatch(fetchOrganizations()).unwrap();
        const targetOrg = result.find((org) => String(org.id) === String(id));
        if (targetOrg) {
          setSchemaName(targetOrg.schema_name);
          setOrganizationServices(targetOrg.services || []);
          setQbLastSyncedAt(targetOrg.qb_last_synced_at);
          setRealmId(targetOrg.realm_id);
        } else {
          // Organization not found with ID
        }
      } catch (error) {
        // Error fetching organizations
      }
    };

    if (id) {
      fetchOrgData();
    }
  }, [dispatch, id]);

  // Filter cards based on organization services
  const getFilteredCards = () => {
    return cardConfigs.filter((card) => {
      // Always show operational and payroll cards
      if (card.key === "operational" || card.key === "payroll") {
        return true;
      }
      // Only show financial card if 'financial' is in the services array
      if (card.key === "financial") {
        return organizationServices.includes("financial");
      }
      return false;
    });
  };

  const handleFileUpload = async (cardKey, file, inputElement) => {
    if (!file) return;

    // Check if schemaName is available before proceeding
    if (cardKey === "payroll" && !schemaName) {
      return;
    }

    setLocalUploadStates((prev) => ({ ...prev, [cardKey]: true }));

    try {
      if (cardKey === "payroll") {
        await dispatch(uploadAdpFile({ file, schemaName })).unwrap();
      }
    } catch (error) {
      // Upload failed
    } finally {
      setLocalUploadStates((prev) => ({ ...prev, [cardKey]: false }));
      if (inputElement) inputElement.value = null;
    }
  };

  const handleFinancialSync = async () => {
    if (!realmId) {
      return;
    }

    if (!schemaName) {
      return;
    }

    setLocalUploadStates((prev) => ({ ...prev, financial: true }));

    try {
      await dispatch(
        syncFinancialData({
          clientId: id,
          month: new Date().toISOString().slice(0, 7), // YYYY-MM format
          files: [],
          realmId: realmId,
          schemaName: schemaName,
        })
      ).unwrap();

      // Update the last synced date after successful sync
      setQbLastSyncedAt(formatDateToDDMMYYYY(new Date().toISOString()));
    } catch (error) {
      // Financial sync failed
    } finally {
      setLocalUploadStates((prev) => ({ ...prev, financial: false }));
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-md border border-gray-200 p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-6">
        {BOOKCLOSURE_CONSTANTS.UPLOAD_SECTION.TITLE}
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {getFilteredCards().map((card) => (
          <div key={card.key}>
            {card.key === "payroll" ? (
              <div className="border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-white shadow-sm flex flex-col h-full">
                <div className="mb-4 flex justify-center">{card.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {card.title}
                </h3>

                <div className="mb-4 flex items-center justify-center gap-2">
                  <span className="inline-block w-2 h-2 rounded-full bg-green-400 animate-pulse"></span>
                  <span className="text-xs text-green-700 font-medium bg-green-50 px-1 rounded">
                    {BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.LAST_SYNCED_LABEL +
                      card.lastSync}
                  </span>
                </div>
                <label
                  className={`px-4 py-2 rounded-xl inline-flex items-center justify-center transition font-medium mt-auto text-sm gap-2 ${
                    schemaName
                      ? "bg-indigo-500 text-white cursor-pointer hover:bg-indigo-700"
                      : "bg-gray-400 text-gray-200 cursor-not-allowed"
                  }`}
                >
                  <Upload className="w-4 h-4" />
                  {(() => {
                    if (localUploadStates[card.key]) return "Uploading...";
                    return "Upload XLSX";
                  })()}
                  <input
                    type="file"
                    accept=".xlsx, .xls"
                    className="hidden"
                    disabled={!schemaName}
                    onChange={(e) =>
                      handleFileUpload(card.key, e.target.files[0], e.target)
                    }
                  />
                </label>
              </div>
            ) : (
              <UploadCard
                title={card.title}
                description={card.description}
                icon={card.icon}
                onSync={
                  card.key === "financial"
                    ? handleFinancialSync
                    : (files) => handleFileUpload(card.key, files)
                }
                isLoading={localUploadStates[card.key]}
                lastSync={
                  card.key === "financial" && qbLastSyncedAt
                    ? formatDateToDDMMYYYY(qbLastSyncedAt)
                    : card.lastSync
                }
                isFinancial={card.key === "financial"}
                realmId={realmId}
                schemaName={schemaName}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

UploadSection.propTypes = {
  uploadStates: PropTypes.object.isRequired,
  id: PropTypes.string.isRequired,
};
