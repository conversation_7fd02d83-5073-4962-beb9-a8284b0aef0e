"use client";

import React from "react";

const PageLoader = ({ message = "Loading...", fullScreen = false }) => {
  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="text-center">
          {/* Animated loader */}
          <div className="relative mx-auto w-20 h-20 mb-4">
            {/* Outer rotating circle */}
            <div className="absolute inset-0 border-4 border-blue-100 rounded-full"></div>
            <div className="absolute inset-0 border-4 border-transparent border-t-blue-600 rounded-full animate-spin"></div>

            {/* Inner pulsing circle */}
            <div className="absolute inset-3 border-4 border-purple-100 rounded-full"></div>
            <div
              className="absolute inset-3 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"
              style={{ animationDirection: "reverse", animationDuration: "1s" }}
            ></div>

            {/* Center dot */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full animate-pulse"></div>
          </div>

          {/* Message */}
          <div className="text-gray-700 font-medium text-lg animate-pulse">
            {message}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-20 min-h-[400px]">
      {/* Animated loader */}
      <div className="relative mx-auto w-20 h-20 mb-4">
        {/* Outer rotating circle */}
        <div className="absolute inset-0 border-4 border-blue-100 rounded-full"></div>
        <div className="absolute inset-0 border-4 border-transparent border-t-blue-600 rounded-full animate-spin"></div>

        {/* Inner pulsing circle */}
        <div className="absolute inset-3 border-4 border-purple-100 rounded-full"></div>
        <div
          className="absolute inset-3 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"
          style={{ animationDirection: "reverse", animationDuration: "1s" }}
        ></div>

        {/* Center dot */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full animate-pulse"></div>
      </div>

      {/* Message */}
      <div className="text-gray-700 font-medium text-lg animate-pulse">
        {message}
      </div>
    </div>
  );
};

export default PageLoader;
