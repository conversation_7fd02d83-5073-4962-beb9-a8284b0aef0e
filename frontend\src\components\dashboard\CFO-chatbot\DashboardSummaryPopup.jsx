"use client";

import { useEffect, useCallback, useMemo, memo } from "react";
import { motion } from "framer-motion";
import { Button } from "../../ui/button";
import { ScrollArea } from "../../ui/scroll-area";
import { X, BarChart3 } from "lucide-react";
import { formatMessage } from "../../../utils/formatMessage";

const DashboardSummaryPopup = memo(function DashboardSummaryPopup({ 
  isOpen, 
  onClose, 
  dashboardSummary,
  selectedMonth,
  isFinancialSelected,
  isOperationsSelected,
}) {
  const formattedContent = useMemo(() => {
    return formatMessage(dashboardSummary);
  }, [dashboardSummary]);

  const dashboardType = useMemo(() => {
    if (isFinancialSelected) return 'Financial';
    if (isOperationsSelected) return 'Operations';
    return 'Dashboard';
  }, [isFinancialSelected, isOperationsSelected]);

  const displayLabel = useMemo(() => {
    return `${dashboardType} - ${selectedMonth} 2025`;
  }, [dashboardType, selectedMonth]);

  // Handle ESC key to close popup - use useCallback to prevent recreation
  const handleEscKey = useCallback((event) => {
    if (event.key === 'Escape' && isOpen) {
      onClose();
    }
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, handleEscKey]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-md"
      onClick={onClose}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="bg-white rounded-3xl shadow-2xl border border-gray-100/50 max-w-5xl w-full mx-4 max-h-[85vh] flex flex-col overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Popup Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/30 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center shadow-lg">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Dashboard Summary
              </h3>
              <p className="text-sm text-gray-500 mt-1 font-medium">Press ESC to close</p>
            </div>
          </div>
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="p-3 hover:bg-white/60 rounded-2xl transition-all duration-300 hover:scale-105"
            title="Close (ESC)"
          >
            <X className="w-6 h-6 text-gray-600" />
          </Button>
        </div>

        {/* Popup Content */}
        <div className="flex-1 flex flex-col overflow-hidden min-h-0">
          <div className="p-6 flex-1 overflow-hidden">
            <div className="h-full flex flex-col">
              <div className="mb-4">
                <h4 className="text-xl font-bold text-indigo-700 flex items-center gap-2">
                  {dashboardType} Overview
                  <span className="text-sm font-normal text-indigo-500 bg-indigo-100 px-3 py-1 rounded-full">
                    {displayLabel}
                  </span>
                </h4>
              </div>
              <ScrollArea className="flex-1 pr-4">
                <div className="pb-4 min-h-full">
                  {formattedContent ? (
                    <div
                      className="dashboard-summary-content"
                      dangerouslySetInnerHTML={{
                        __html: formattedContent,
                      }}
                    />
                  ) : (
                    <div className="text-center text-gray-400 py-8">
                      <p>No summary available</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
        </div>

        {/* Popup Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200/30 bg-gradient-to-r from-gray-50/50 to-indigo-50/30">
          <div className="text-sm text-gray-500">
            💡 Tip: Press ESC to close quickly
          </div>
          <Button
            onClick={onClose}
            variant="outline"
            className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 hover:border-indigo-300 px-6 py-2 rounded-xl font-medium transition-all duration-200 hover:scale-105"
            title="Close (ESC)"
          >
            Close
          </Button>
        </div>
      </motion.div>
    </div>
  );
});

export default DashboardSummaryPopup;
