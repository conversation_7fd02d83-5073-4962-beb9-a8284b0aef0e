import React, { useState, useEffect, useCallback, useMemo } from "react";
import PdfViewer from "@/components/dashboard/PDFViewer";
import CFOInsightsPage from "@/components/dashboard/CFOInsightsPage";
import {
  FinancePath,
  OperationsPath,
  monthToFile,
  chpMonthToFile,
  chpOperationsMonthToFile,
  chpPayrollMonthToFile,
  dentalMonthToFile,
  dentalOperationsMonthToFile,
  dentalPayrollMonthToFile,
} from "@/utils/data/dashboard";
import Sidebar from "./Sidebar";
import MobileMenu from "./MobileMenu";
import { DASHBOARD_CONSTANTS } from "@/utils/constants/dashboard";
import { useSelector, useDispatch } from 'react-redux';
import { downloadFile } from '@/redux/Thunks/fileOperations';
import tokenStorage from "@/lib/tokenStorage";
import { useSearchParams } from "next/navigation";

export default function Dashboard() {
  const [userEmail, setUserEmail] = useState("");
  const [userRole, setUserRole] = useState("");
  const [availableMonths, setAvailableMonths] = useState(
    DASHBOARD_CONSTANTS.SIDEBAR.MONTHS
  );
  const [months, setMonths] = useState(
    DASHBOARD_CONSTANTS.SIDEBAR.MONTHS.at(-1)
  );
  const [pageToView, setPageToView] = useState(1);
  const [isFinancialSelected, setIsFinancialSelected] = useState(true);
  const [isOperationsSelected, setIsOperationsSelected] = useState(false);
  const [isPayrollSelected, setIsPayrollSelected] = useState(false);
  const [showCFOInsights, setShowCFOInsights] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [selectedDashboard, setSelectedDashboard] = useState(""); // New state for dashboard selection
  const [organizationName, setOrganizationName] = useState(null);

  const searchParams = useSearchParams();

  // Get user data and determine available months
  useEffect(() => {
    const userData = tokenStorage.getUserData();
    if (userData?.email) {
      setUserEmail(userData.email);
      setUserRole(userData.role?.name);

      // Check if user is CHP or Dental (case-insensitive)
      const normalizedEmail = userData.email.toLowerCase();
      if (
        normalizedEmail === "<EMAIL>" ||
        normalizedEmail === "<EMAIL>"
      ) {
        setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
        // Set default month to June for these users
        setMonths("June");
      } else {
        setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.MONTHS);
        setMonths(DASHBOARD_CONSTANTS.SIDEBAR.MONTHS.at(-1));
      }
    }
  }, []);

  // Auto-select dashboard and organization based on URL parameter
  useEffect(() => {
    const dashboardParam = searchParams.get("dashboard");
    const organizationParam = searchParams.get("organization");
    
    if (organizationParam) {
      setOrganizationName(decodeURIComponent(organizationParam));
    }
    
    if (
      dashboardParam &&
      (dashboardParam === "chp" || dashboardParam === "dental")
    ) {
      setSelectedDashboard(dashboardParam);
      setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
      setMonths("June");
      setIsFinancialSelected(true);
      setIsOperationsSelected(false);
      setIsPayrollSelected(false);
    }
  }, [searchParams]);

  // Get the appropriate file mapping based on user type and section
  const getFileMapping = useCallback(() => {
    let mapping;
    
    // Normalize email for case-insensitive comparison
    const normalizedEmail = userEmail?.toLowerCase() || "";

    // If admin has selected a specific dashboard
    if (userRole === "admin" && selectedDashboard) {
      if (selectedDashboard === "chp") {
        if (isOperationsSelected) mapping = chpOperationsMonthToFile;
        else if (isPayrollSelected) mapping = chpPayrollMonthToFile;
        else mapping = chpMonthToFile;
      } else if (selectedDashboard === "dental") {
        if (isOperationsSelected) mapping = dentalOperationsMonthToFile;
        else if (isPayrollSelected) mapping = dentalPayrollMonthToFile;
        else mapping = dentalMonthToFile;
      }
    } else if (normalizedEmail === "<EMAIL>") {
      // Original logic for specific users (case-insensitive)
      if (isOperationsSelected) mapping = chpOperationsMonthToFile;
      else if (isPayrollSelected) mapping = chpPayrollMonthToFile;
      else mapping = chpMonthToFile;
    } else if (normalizedEmail === "<EMAIL>") {
      // Case-insensitive comparison
      if (isOperationsSelected) mapping = dentalOperationsMonthToFile;
      else if (isPayrollSelected) mapping = dentalPayrollMonthToFile;
      else mapping = dentalMonthToFile;
    } else {
      mapping = monthToFile;
    }

    return mapping;
  }, [
    userEmail,
    userRole,
    selectedDashboard,
    isOperationsSelected,
    isPayrollSelected,
  ]);

  // Memoize PDF path to prevent unnecessary re-renders
  const pdfPath = useMemo(() => {
    const fileMapping = getFileMapping();
    const selectedFile = fileMapping[months];

    // For admin with selected dashboard
    if (userRole === "admin" && selectedDashboard) {
      return selectedFile || FinancePath;
    }

    // For CHP and Dental users, use the appropriate file mapping based on section (case-insensitive)
    const normalizedEmail = userEmail?.toLowerCase() || "";
    if (
      normalizedEmail === "<EMAIL>" ||
      normalizedEmail === "<EMAIL>"
    ) {
      return selectedFile || FinancePath;
    }

    // For other users, use the original logic
    if (isOperationsSelected && months === "June") {
      return OperationsPath;
    }
    return selectedFile || FinancePath;
  }, [
    isOperationsSelected,
    months,
    getFileMapping,
    userEmail,
    userRole,
    selectedDashboard,
  ]);

  // Memoize page number to prevent unnecessary re-renders
  const page = useMemo(() => {
    return isFinancialSelected || isOperationsSelected || isPayrollSelected
      ? pageToView
      : 1;
  }, [
    isFinancialSelected,
    isOperationsSelected,
    isPayrollSelected,
    pageToView,
  ]);

  // Memoize PDF viewer props to prevent unnecessary re-renders
  const pdfViewerProps = useMemo(
    () => ({
      url: pdfPath,
      pageToView: page,
    }),
    [pdfPath, page]
  );
  const dispatch = useDispatch();
  const { downloading } = useSelector((state) => state.fileOperations || {});

  // Dashboard selection component for admins
  const DashboardSelector = () => {
    if (userRole !== "admin") return null;

    return (
      <div
        className="flex flex-col lg:flex-row w-full h-full min-h-0"
        style={{ height: "100vh" }}
      >
        <div className="flex-1 h-full overflow-y-auto overflow-x-hidden flex items-center justify-center bg-gray-50 min-w-0 p-2 sm:p-4">
          <div className="w-full max-w-4xl">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">
                  Select Dashboard
                </h1>
                <p className="text-gray-600">
                  Choose which dashboard you want to view
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* CHP Dashboard Button */}
                <button
                  onClick={() => {
                    setSelectedDashboard("chp");
                    setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
                    setMonths("June");
                    setIsFinancialSelected(true);
                    setIsOperationsSelected(false);
                    setIsPayrollSelected(false);
                  }}
                  className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 p-8 text-white transition-all duration-300 hover:from-blue-600 hover:to-blue-700 hover:scale-105 hover:shadow-xl"
                >
                  <div className="relative z-10">
                    <div className="mb-4">
                      <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg
                          className="w-8 h-8"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-bold mb-2">CHP Dashboard</h3>
                      <p className="text-blue-100 mb-4">
                        View CHP financial, operations, and payroll data
                      </p>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-blue-100">Finance Reports:</span>
                        <span className="font-semibold">
                          June, July, August
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-blue-100">Operations:</span>
                        <span className="font-semibold">June</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-blue-100">Payroll:</span>
                        <span className="font-semibold">June, July</span>
                      </div>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                </button>

                {/* Dental Dashboard Button */}
                <button
                  onClick={() => {
                    setSelectedDashboard("dental");
                    setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
                    setMonths("June");
                    setIsFinancialSelected(true);
                    setIsOperationsSelected(false);
                    setIsPayrollSelected(false);
                  }}
                  className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-green-500 to-green-600 p-8 text-white transition-all duration-300 hover:from-green-600 hover:to-green-700 hover:scale-105 hover:shadow-xl"
                >
                  <div className="relative z-10">
                    <div className="mb-4">
                      <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg
                          className="w-8 h-8"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-bold mb-2">
                        Dental Dashboard
                      </h3>
                      <p className="text-green-100 mb-4">
                        View Dental financial, operations, and payroll data
                      </p>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-green-100">Finance Reports:</span>
                        <span className="font-semibold">
                          June, July, August
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-green-100">Operations:</span>
                        <span className="font-semibold">June</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-green-100">Payroll:</span>
                        <span className="font-semibold">June, July</span>
                      </div>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (!pdfPath && !showCFOInsights) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        {DASHBOARD_CONSTANTS.NO_PDF_MESSAGE}
      </div>
    );
  }

  const handleDownload = async () => {
    if (!pdfPath) return;
    try {
      // Extract filename from path for better naming
      const filename =
        pdfPath.split("/").pop() ||
        DASHBOARD_CONSTANTS.DEFAULT_DOWNLOAD_FILENAME;

      // Try the improved fetch method first
      await download(pdfPath, filename);
    } catch (error) {
      console.warn(
        "Primary download failed, trying alternative method:",
        error
      );
      try {


        // Fallback to Redux download method if fetch fails
        await dispatch(downloadFile({ url: pdfPath, filename }));
      } catch (xhrError) {
        console.error("Both download methods failed:", xhrError);
        // Error handling is done in the hook
      }
    }
  };

  const handleGenAIClick = () => setShowCFOInsights(true);
  const handleBackFromCFOInsights = () => setShowCFOInsights(false);

  const handleDashboardSwitch = (newDashboard) => {
    setSelectedDashboard(newDashboard);
    setAvailableMonths(DASHBOARD_CONSTANTS.SIDEBAR.ALL_MONTHS);
    setMonths("June");
    setIsFinancialSelected(true);
    setIsOperationsSelected(false);
    setIsPayrollSelected(false);
  };

  // Show dashboard selector for admins who haven't selected a dashboard
  if (userRole === "admin" && !selectedDashboard) {
    return <DashboardSelector />;
  }

  const sidebarProps = {
    months,
    setMonths,
    pageToView,
    setPageToView,
    isFinancialSelected,
    setIsFinancialSelected,
    isOperationsSelected,
    setIsOperationsSelected,
    isPayrollSelected,
    setIsPayrollSelected,
    availableMonths,
    onDownload: handleDownload,
    onGenAIClick: handleGenAIClick,
    userRole,
    selectedDashboard,
    onDashboardChange: setSelectedDashboard,
  };

  if (showCFOInsights) {
    return (
      <div
        className="flex flex-col lg:flex-row w-full h-full min-h-0"
        style={{ height: "100vh" }}
      >
        <MobileMenu
          isOpen={isMobileMenuOpen}
          onToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          <Sidebar {...sidebarProps} />
        </MobileMenu>
        <div className="hidden lg:block w-64 flex-shrink-0">
          <Sidebar {...sidebarProps} />
        </div>
        <div className="flex-1 h-full overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 pl-4">
          <CFOInsightsPage
            onBack={handleBackFromCFOInsights}
            selectedMonth={months}
            selectedMonthKey={months}
            selectedPage={pageToView}
            isFinancialSelected={isFinancialSelected}
            isOperationsSelected={isOperationsSelected}
            isPayrollSelected={isPayrollSelected}
            selectedDashboard={selectedDashboard || (userEmail?.toLowerCase() === "<EMAIL>" ? "chp" : userEmail?.toLowerCase() === "<EMAIL>" ? "dental" : "chp")}
            organizationName={organizationName}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col lg:flex-row w-full h-full min-h-0"
      style={{ height: "100vh" }}
    >
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        <Sidebar {...sidebarProps} />
      </MobileMenu>
      <div className="hidden lg:block w-56 xl:w-64 flex-shrink-0">
        <Sidebar {...sidebarProps} />
      </div>


      <div className="flex-1 h-full overflow-y-auto overflow-x-hidden flex items-start justify-center bg-gray-50 min-w-0 p-2 sm:p-4">
        <div className="w-full max-w-7xl">
          <PdfViewer {...pdfViewerProps} />
        </div>
      </div>
    </div>
  );
}
