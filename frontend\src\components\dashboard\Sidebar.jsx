import { useEffect, useState } from "react";
import { But<PERSON> } from "../ui/button";
import { <PERSON>Left, <PERSON>rk<PERSON>, User, LogIn } from "lucide-react";
import LogoutButton from "../ui/logout-button";
import { useRouter } from "next/navigation";
import tokenStorage from "@/lib/tokenStorage";


import { DASHBOARD_CONSTANTS } from "@/utils/constants/dashboard";
import { getVersionedImage } from "@/utils/imageVersions";

export default function Sidebar({
  months,
  setMonths,
  pageToView,
  setPageToView,
  isFinancialSelected,
  setIsFinancialSelected,
  isOperationsSelected,
  setIsOperationsSelected,
  isPayrollSelected,
  setIsPayrollSelected,
  availableMonths,
  onDownload,
  onGenAIClick = () => {},
  userRole,
  selectedDashboard,
  onDashboardChange,
}) {
  const router = useRouter();
  const [isUserAdmin, setIsUserAdmin] = useState(false);
  const [userData, setUserData] = useState(null);

  useEffect(() => {
    const user = tokenStorage.getUserData();
    setUserData(user);
    setIsUserAdmin(user?.role === "admin");
  }, []);

  const handleBack = () => {
    router.push("/listing");
  };

  const handleCFOInsightsClick = () => {
    if (onGenAIClick) {
      onGenAIClick();
    }
  };

  const handleUserProfileClick = () => {
    router.push("/profile");
  };

  return (
    <>
      <div
        className="flex flex-col gap-2 sm:gap-3 w-full lg:w-56 xl:w-64 p-3 sm:p-4 lg:p-5 shadow-md h-full overflow-auto"
        style={{ backgroundColor: DASHBOARD_CONSTANTS.COLORS.PRIMARY }}
      >
        {isUserAdmin && (
          <Button
            variant="default"
            leftIcon={<ArrowLeft />}
            onClick={() => handleBack()}
            className={`w-full text-xs sm:text-sm border-none focus:outline-none focus:ring-2 focus:ring-white/40 py-2 px-3`}
          >
            <span className="hidden sm:inline">{DASHBOARD_CONSTANTS.SIDEBAR.BACK_BUTTON_TEXT}</span>
            <span className="sm:hidden">Back</span>
          </Button>
        )}


        <Button
          variant="default"
          onClick={() => onDownload()}
          className={`w-full text-xs sm:text-sm border-none focus:outline-none focus:ring-2 focus:ring-white/40 py-2 px-3`}
        >
          <span className="hidden sm:inline">{DASHBOARD_CONSTANTS.SIDEBAR.DOWNLOAD_BUTTON_TEXT}</span>
          <span className="sm:hidden">Download</span>
        </Button>

        <div className="w-full flex flex-col gap-2">
          <Button
            type="button"
            aria-expanded={isFinancialSelected}
            onClick={() => {
              setIsFinancialSelected(!isFinancialSelected);
              // Close Operations when Financial is opened
              if (!isFinancialSelected) {
                setIsOperationsSelected(false);
                setMonths("June");
              }
            }}
            className={`w-full px-2 py-1.5 rounded-md font-semibold flex items-center justify-between transition-colors duration-200 focus:outline-none border-none text-xs sm:text-sm
              ${
                isFinancialSelected
                  ? `shadow-sm`
                  : `bg-transparent hover:bg-[${DASHBOARD_CONSTANTS.COLORS.HOVER}]`
              }`}
          >
            <span>{DASHBOARD_CONSTANTS.SIDEBAR.FINANCIAL_TAB}</span>
            <span
              className={`ml-2 text-xs transition-transform duration-200 ${
                isFinancialSelected
                  ? "text-white rotate-180"
                  : "text-white/70 rotate-0"
              }`}
              style={{ display: "inline-block" }}
            >
              ▲
            </span>
          </Button>
          <div
            className={`overflow-hidden transition-all duration-300 ${
              isFinancialSelected
                ? "max-h-60 opacity-100"
                : "max-h-0 opacity-0 pointer-events-none"
            }`}
          >
                <div className="pl-3 sm:pl-4 py-1 space-y-1">
              {availableMonths.map((month) => {
                // Convert month name to Month-YY format (e.g., "June" -> "June-25")
                const currentYear = new Date().getFullYear();
                const yearSuffix = currentYear.toString().slice(-2);
                const monthYear = `${month}-${yearSuffix}`;
                
                return (
                <Button
                  key={month}
                  variant={months === month ? "secondary" : "ghost"}
                    className={`w-full justify-start text-white text-xs sm:text-sm py-1 px-2 ${
                    months === month
                      ? `font-bold bg-[${DASHBOARD_CONSTANTS.COLORS.SECONDARY}]`
                      : "hover:bg-[${DASHBOARD_CONSTANTS.COLORS.HOVER}]"
                  }`}
                  onClick={() => setMonths(month)}
                >
                    {monthYear}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>

        <div className="w-full flex flex-col gap-2">
          <Button
            type="button"
            aria-expanded={isOperationsSelected}
            onClick={() => {
              setIsOperationsSelected(!isOperationsSelected);
              // Close Financial and Payroll when Operations is opened
              if (!isOperationsSelected) {
                setIsFinancialSelected(false);
                setIsPayrollSelected(false);
                // Don't set months here - let user click on June-25 to load PDF
              }
            }}
            className={`w-full px-2 py-1.5 rounded-md font-semibold flex items-center justify-between transition-colors duration-200 focus:outline-none border-none text-xs sm:text-sm
              ${
                isOperationsSelected
                  ? `shadow-sm`
                  : `bg-transparent hover:bg-[${DASHBOARD_CONSTANTS.COLORS.HOVER}]`
              }`}
          >
            <span>{DASHBOARD_CONSTANTS.SIDEBAR.OPERATIONS_TAB}</span>
            <span
              className={`ml-2 text-xs transition-transform duration-200 ${
                isOperationsSelected
                  ? "text-white rotate-180"
                  : "text-white/70 rotate-0"
              }`}
              style={{ display: "inline-block" }}
            >
              ▲
            </span>
          </Button>
          <div
            className={`overflow-hidden transition-all duration-300 ${
              isOperationsSelected
                ? "max-h-60 opacity-100"
                : "max-h-0 opacity-0 pointer-events-none"
            }`}
          >
                <div className="pl-3 sm:pl-4 py-1 space-y-1">
              <Button
                variant={months === "June" ? "secondary" : "ghost"}
                className={`w-full justify-start text-white text-xs sm:text-sm py-1.5 px-2 ${
                  months === "June"
                    ? `font-bold bg-[${DASHBOARD_CONSTANTS.COLORS.SECONDARY}]`
                    : "hover:bg-[${DASHBOARD_CONSTANTS.COLORS.HOVER}]"
                }`}
                onClick={() => setMonths("June")}
              >
                June-25
              </Button>
            </div>
          </div>
        </div>

        <div className="w-full flex flex-col gap-2">
          <Button
            type="button"
            aria-expanded={isPayrollSelected}
            onClick={() => {
              setIsPayrollSelected(!isPayrollSelected);
              // Close Financial and Operations when Payroll is opened
              if (!isPayrollSelected) {
                setIsFinancialSelected(false);
                setIsOperationsSelected(false);
                setMonths("June");
              }
            }}
            className={`w-full px-2 py-1.5 rounded-md font-semibold flex items-center justify-between transition-colors duration-200 focus:outline-none border-none text-xs sm:text-sm
              ${
                isPayrollSelected
                  ? `shadow-sm`
                  : `bg-transparent hover:bg-[${DASHBOARD_CONSTANTS.COLORS.HOVER}]`
              }`}
          >
            <span>{DASHBOARD_CONSTANTS.SIDEBAR.PAYROLL_TAB}</span>
            <span
              className={`ml-2 text-xs transition-transform duration-200 ${
                isPayrollSelected
                  ? "text-white rotate-180"
                  : "text-white/70 rotate-0"
              }`}
              style={{ display: "inline-block" }}
            >
              ▲
            </span>
          </Button>
          <div
            className={`overflow-hidden transition-all duration-300 ${
              isPayrollSelected
                ? "max-h-60 opacity-100"
                : "max-h-0 opacity-0 pointer-events-none"
            }`}
          >
                <div className="pl-3 sm:pl-4 py-1 space-y-1">
              <Button
                variant={months === "June" ? "secondary" : "ghost"}
                className={`w-full justify-start text-white text-xs sm:text-sm py-1.5 px-2 ${
                  months === "June"
                    ? `font-bold bg-[${DASHBOARD_CONSTANTS.COLORS.SECONDARY}]`
                    : "hover:bg-[${DASHBOARD_CONSTANTS.COLORS.HOVER}]"
                }`}
                onClick={() => setMonths("June")}
              >
                June-25
              </Button>
              <Button
                variant={months === "July" ? "secondary" : "ghost"}
                className={`w-full justify-start text-white text-xs sm:text-sm py-1.5 px-2 ${
                  months === "July"
                    ? `font-bold bg-[${DASHBOARD_CONSTANTS.COLORS.SECONDARY}]`
                    : "hover:bg-[${DASHBOARD_CONSTANTS.COLORS.HOVER}]"
                }`}
                onClick={() => setMonths("July")}
              >
                July-25
              </Button>
              <Button
                variant={months === "August" ? "secondary" : "ghost"}
                className={`w-full justify-start text-white text-xs sm:text-sm py-1.5 px-2 ${
                  months === "August"
                    ? `font-bold bg-[${DASHBOARD_CONSTANTS.COLORS.SECONDARY}]`
                    : "hover:bg-[${DASHBOARD_CONSTANTS.COLORS.HOVER}]"
                }`}
                onClick={() => setMonths("August")}
              >
                August-25
              </Button>
            </div>
          </div>
        </div>

        <div className="mt-auto">
          <Button
            onClick={handleCFOInsightsClick}
            variant="outline"
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 mb-4 text-xs sm:text-sm py-2 px-3"
          >
            <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">CFO Insights</span>
            <span className="sm:hidden">AI</span>
          </Button>
        </div>

        <div className="p-2 sm:p-3 border-t border-white/20 w-full">
          {/* User Info Section */}
          <button
            onClick={handleUserProfileClick}
            className="bg-white/10 rounded-lg p-3 mb-3 w-full text-left hover:bg-white/20 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white/40"
          >
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-white text-xs font-semibold truncate">
                  {userData?.name || "User"}
                </div>
                <div className="text-white/70 text-xs truncate">
                  {userData?.email || "<EMAIL>"}
                </div>
                <div className="text-white/60 text-xs">
                  {userData?.role || "Role"}
                </div>
              </div>
            </div>
          </button>
          <LogoutButton />
        </div>
      </div>
    </>
  );
}
