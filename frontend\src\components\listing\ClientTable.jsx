"use client";

import { useState, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import TableHeader from "../common/TableHeader";
import StatusBadge from "../common/StatusBadge";
import Pagination from "../common/Pagination";
import TableActions from "../common/TableActions";
import { LISTING_CONSTANTS } from "@/utils/constants/listing";
import { getInitials } from "@/utils/methods";
import PageLoader from "../common/PageLoader";

export default function ClientTable({
  searchTerm = LISTING_CONSTANTS.SEARCH.ALL,
  statusFilter = LISTING_CONSTANTS.STATUS_FILTER.ALL,
  organizations = [],
  loading = false,
  error = null,
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const itemsPerPage = LISTING_CONSTANTS.PAGINATION.ITEMS_PER_PAGE;

  const router = useRouter();

  // Replace clientsData with organizations
  const filteredClients = (organizations ?? []).filter((client) => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch =
      !!client.name?.toLowerCase()?.includes(searchLower) ||
      !!client.email?.toLowerCase()?.includes(searchLower) ||
      !!client.schema_name?.toLowerCase()?.includes(searchLower);
    const matchesStatus =
      statusFilter === "All Status" ||
      (statusFilter === "Active" && client.is_active) ||
      (statusFilter === "Inactive" && !client.is_active);
    return matchesSearch && matchesStatus;
  });

  // Sort functionality
  const sortedClients = useMemo(() => {
    if (!filteredClients || filteredClients.length === 0) return [];

    return [...filteredClients].sort((a, b) => {
      if (!sortConfig.key) return 0;

      // Handle null/undefined values
      const aVal = a[sortConfig.key];
      const bVal = b[sortConfig.key];

      if (!aVal && !bVal) return 0;
      if (!aVal) return 1;
      if (!bVal) return -1;

      const aValLower = String(aVal).toLowerCase();
      const bValLower = String(bVal).toLowerCase();

      if (sortConfig.direction === "asc") {
        return aValLower < bValLower ? -1 : aValLower > bValLower ? 1 : 0;
      } else {
        return aValLower > bValLower ? -1 : aValLower < bValLower ? 1 : 0;
      }
    });
  }, [filteredClients, sortConfig]);

  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(sortedClients.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedClients = sortedClients.slice(
      startIndex,
      startIndex + itemsPerPage
    );
    return { totalPages, startIndex, paginatedClients };
  }, [sortedClients, currentPage, itemsPerPage]);

  const { totalPages, startIndex, paginatedClients } = paginationData;

  const handleSort = useCallback((key) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === "asc" ? "desc" : "asc",
    }));
  }, []);

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return (
        <svg
          className="w-4 h-4 ml-1 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 9l4-4 4 4m0 6l-4 4-4-4"
          />
        </svg>
      );
    }

    return sortConfig.direction === "asc" ? (
      <svg
        className="w-4 h-4 ml-1 text-blue-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M5 15l7-7 7 7"
        />
      </svg>
    ) : (
      <svg
        className="w-4 h-4 ml-1 text-blue-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 9l-7 7-7-7"
        />
      </svg>
    );
  };

  const renderStatusBadge = (status) => {
    return <StatusBadge status={status} variant="outline" />;
  };

  // Format date from timestamp to DD/MM/YYYY
  const formatDate = (dateString) => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      const day = String(date.getDate()).padStart(2, "0");
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch (error) {
      return null;
    }
  };

  // Render sync badges for each service
  const renderSyncBadges = (client) => {
    const syncData = [
      {
        name: "Operational",
        date: formatDate(client.sikka_last_synced_at),
        bgColor: "bg-blue-100",
        textColor: "text-blue-700",
      },
      {
        name: "Payroll",
        date: formatDate(client.adp_last_synced_at),
        bgColor: "bg-green-100",
        textColor: "text-green-700",
      },
      {
        name: "Financial",
        date: formatDate(client.qb_last_synced_at),
        bgColor: "bg-orange-100",
        textColor: "text-orange-700",
      },
    ];

    return (
      <div className="flex flex-col gap-2 items-start">
        {syncData.map((item, index) => (
          <div
            key={index}
            className={`inline-flex items-center px-2 py-1 rounded-full w-fit ${item.bgColor} ${item.textColor} text-xs font-medium`}
          >
            <span>{item.name}:</span>
            <span className="ml-1">{item.date || "not synced yet"}</span>
          </div>
        ))}
      </div>
    );
  };

  const handleViewDetails = (client) => {
    const organizationName = encodeURIComponent(client.name);
    router.push(`/dashboard?organization=${organizationName}`);
  };

  const handleBookkeeping = (clientId) => {
    router.push(`/book-closure/${clientId}`);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Loading / Error */}
      {loading ? (
        <PageLoader message="Loading clients..." />
      ) : error ? (
        <div className="p-6 text-red-500 font-bold text-center">
          {typeof error === "string" ? error : "Failed to load clients."}
        </div>
      ) : (
        <>
          {/* Header */}
          <TableHeader
            title={LISTING_CONSTANTS.MESSAGES.CLIENTS_COUNT}
            count={filteredClients.length}
            lastUpdated={LISTING_CONSTANTS.MESSAGES.LAST_UPDATED}
          />

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full table-fixed align-middle">
              <colgroup>
                <col style={{ width: "20%" }} />
                <col style={{ width: "25%" }} />
                <col style={{ width: "10%" }} />
                <col style={{ width: "20%" }} />
                <col style={{ width: "25%" }} />
              </colgroup>
              <thead>
                <tr className="border-b border-gray-200 bg-gray-100">
                  <th
                    className="px-4 py-4 text-left text-md font-semibold text-gray-700 cursor-pointer hover:text-blue-600 transition-colors duration-200"
                    onClick={() => handleSort("name")}
                  >
                    <div className="flex items-center">
                      {LISTING_CONSTANTS.TABLE_HEADERS.CLIENT_NAME}
                      {getSortIcon("name")}
                    </div>
                  </th>
                  <th
                    className="px-4 py-4 text-left text-md font-semibold text-gray-700 cursor-pointer hover:text-blue-600 transition-colors duration-200"
                    onClick={() => handleSort("email")}
                  >
                    <div className="flex items-center">
                      {LISTING_CONSTANTS.TABLE_HEADERS.EMAIL}
                      {getSortIcon("email")}
                    </div>
                  </th>
                  <th className="px-2 py-4 text-left text-md font-semibold text-gray-700">
                    {LISTING_CONSTANTS.TABLE_HEADERS.STATUS}
                  </th>
                  <th className="px-4 py-4 text-left text-md font-semibold text-gray-700">
                    Last synced at
                  </th>
                  <th className="px-4 py-4 text-left text-md font-semibold text-gray-700">
                    {LISTING_CONSTANTS.TABLE_HEADERS.ACTIONS}
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedClients.map((client, index) => (
                  <tr
                    key={client.id}
                    className={`align-middle transition-all duration-200 ${
                      LISTING_CONSTANTS.TABLE_STYLING.ROW_HOVER
                    } border-b ${
                      index === paginatedClients.length - 1
                        ? "border-b-0"
                        : "border-gray-200"
                    }`}
                    onMouseEnter={() => setHoveredRow(client.id)}
                    onMouseLeave={() => setHoveredRow(null)}
                  >
                    <td className="px-3 py-3 min-w-0">
                      <div className="flex items-center group min-w-0">
                        <div
                          className={`w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 ${
                            hoveredRow === client.id
                              ? LISTING_CONSTANTS.TABLE_STYLING.AVATAR_HOVER
                              : ""
                          }`}
                        >
                          <span className="text-white text-md font-bold">
                            {getInitials(client.name)}
                          </span>
                        </div>
                        <div className="ml-4 min-w-0">
                          <div
                            className={`text-md font-semibold text-gray-900 transition-colors duration-200 truncate ${LISTING_CONSTANTS.TABLE_STYLING.NAME_HOVER}`}
                          >
                            {client.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-3 min-w-0">
                      <div className="text-md text-green-700">
                        {client.email}
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      {renderStatusBadge(
                        client.is_active ? "Active" : "Inactive"
                      )}
                    </td>
                    <td className="px-3 py-3">{renderSyncBadges(client)}</td>
                    <td className="px-3 py-3">
                      <div
                        className={`transition-all duration-300 ${
                          hoveredRow === client.id
                            ? LISTING_CONSTANTS.TABLE_STYLING.ACTION_HOVER
                            : ""
                        }`}
                      >
                        <TableActions
                          item={client}
                          actions={[
                            {
                              label: LISTING_CONSTANTS.ACTIONS.VIEW_DETAILS,
                              variant: "outline",
                              onClick: handleViewDetails,
                            },
                            {
                              label:
                                LISTING_CONSTANTS.ACTIONS.SUBMIT_BOOK_CLOSURE,
                              variant: "default",
                              onClick: () => handleBookkeeping(client.id),
                            },
                          ]}
                        />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            totalItems={filteredClients.length}
            startIndex={startIndex}
            endIndex={startIndex + itemsPerPage}
          />
        </>
      )}
    </div>
  );
}
