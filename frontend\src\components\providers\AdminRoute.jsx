"use client";

import PropTypes from "prop-types";
import ProtectedRoute from "./ProtectedRoute";

/**
 * AdminRoute component - wrapper for admin-only routes
 * Automatically sets requiredRole to "admin"
 */
export default function AdminRoute({
  children,
  fallbackComponent = null,
  redirectTo = "/dashboard", // Default redirect for non-admin users
}) {
  return (
    <ProtectedRoute
      requiredRole="admin"
      fallbackComponent={fallbackComponent}
      redirectTo={redirectTo}
    >
      {children}
    </ProtectedRoute>
  );
}

// PropTypes validation
AdminRoute.propTypes = {
  children: PropTypes.node.isRequired,
  fallbackComponent: PropTypes.node,
  redirectTo: PropTypes.string,
};
