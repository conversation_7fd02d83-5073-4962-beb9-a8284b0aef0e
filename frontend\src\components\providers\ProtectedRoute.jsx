"use client";

import { useEffect, useCallback, useState } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import PropTypes from "prop-types";
import { Loader } from "@/components/ui/loading";
import tokenStorage from "@/lib/tokenStorage.js";

/**
 * Enhanced ProtectedRoute component with better SSR support
 * Handles both client-side and SSR authentication scenarios
 */
export default function ProtectedRoute({
  children,
  requiredRole = null,
  fallbackComponent = null,
  redirectTo = null,
}) {
  const { user, loading } = useSelector((state) => state.auth);
  const [isHydrated, setIsHydrated] = useState(false);
  const [authChecked, setAuthChecked] = useState(false);
  const router = useRouter();

  // Get user role with fallback to token data
  const getUserRole = useCallback(() => {
    // Try Redux state first
    if (user?.role) return user.role;

    // Fallback to token storage
    const tokenUser = tokenStorage.getUserDataFromToken();
    return tokenUser?.role || null;
  }, [user]);

  // Get redirect URL based on user role
  const getRedirectUrl = useCallback(
    (userRole) => {
      if (redirectTo) return redirectTo;

      if (userRole === "admin") {
        return "/listing";
      }
      return "/dashboard";
    },
    [redirectTo]
  );

  // Check authentication status
  const checkAuthentication = useCallback(() => {
    // Check token storage for authentication
    const hasValidToken =
      tokenStorage.isAuthenticated() && !tokenStorage.isAccessTokenExpired();

    if (!hasValidToken) {
      return { isAuthenticated: false, user: null };
    }

    // Get user data from token or Redux state
    const currentUser = user || tokenStorage.getUserDataFromToken();

    return {
      isAuthenticated: true,
      user: currentUser,
    };
  }, [user]);

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Main authentication effect
  useEffect(() => {
    // Skip if not hydrated or still loading
    if (!isHydrated || loading) return;

    const { isAuthenticated } = checkAuthentication();

    if (!isAuthenticated) {
      console.log(
        "ProtectedRoute: User not authenticated, redirecting to login"
      );
      router.push("/login");
      return;
    }

    // Check role if required
    if (requiredRole) {
      const userRole = getUserRole();
      if (userRole !== requiredRole) {
        console.log(
          `ProtectedRoute: User role '${userRole}' doesn't match required role '${requiredRole}'`
        );
        const redirectUrl = getRedirectUrl(userRole);
        router.push(redirectUrl);
        return;
      }
    }

    setAuthChecked(true);
  }, [
    isHydrated,
    loading,
    user,
    requiredRole,
    router,
    getUserRole,
    getRedirectUrl,
    checkAuthentication,
  ]);

  // Show loading states
  if (!isHydrated) {
    return <Loader text="Initializing..." />;
  }

  if (loading) {
    return <Loader text="Checking authentication..." />;
  }

  // Check authentication after hydration
  const { isAuthenticated } = checkAuthentication();

  if (!isAuthenticated) {
    return fallbackComponent || <Loader text="Redirecting to login..." />;
  }

  // Check role authorization
  if (requiredRole) {
    const userRole = getUserRole();
    if (userRole !== requiredRole) {
      return fallbackComponent || <Loader text="Redirecting..." />;
    }
  }

  // Only render children if authentication is fully checked
  if (!authChecked) {
    return <Loader text="Verifying access..." />;
  }

  return children;
}

// PropTypes validation
ProtectedRoute.propTypes = {
  children: PropTypes.node.isRequired,
  requiredRole: PropTypes.string,
  fallbackComponent: PropTypes.node,
  redirectTo: PropTypes.string,
};
