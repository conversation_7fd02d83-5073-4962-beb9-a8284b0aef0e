import { createSlice } from "@reduxjs/toolkit";
import {
  startChatSession,
  sendChatMessage,
} from "../Thunks/chat";
import { MESSAGE_TYPES, CHAT_ERRORS, CHAT_MESSAGES } from "../../utils/constants/chat";
import { createMessage, extractAnswerFromResponse } from "../../utils/chat";

// Initial state with better organization
const initialState = {
  // Session state
  session: {
    id: null,
    filename: null,
    isActive: false,
  },
  
  // Chat data
  messages: [],
  dashboardSummary: null,
  
  // UI state
  ui: {
    isLoading: false,
    hasWelcomed: false,
    error: null,
  },
};

const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    // Error management
    clearError: (state) => {
      state.ui.error = null;
    },
    
    // Message management
    addMessage: (state, action) => {
      const newMessage = action.payload;
      // Check if message with same ID already exists to prevent duplicates
      const existingMessage = state.messages.find(msg => msg.id === newMessage.id);
      if (!existingMessage) {
        state.messages.push(newMessage);
      }
    },
    
    clearMessages: (state) => {
      state.messages = [];
    },
    
    // UI state management
    setHasWelcomed: (state, action) => {
      state.ui.hasWelcomed = action.payload;
    },
    
    setLoading: (state, action) => {
      state.ui.isLoading = action.payload;
    },
    
    // Session management
    setSession: (state, action) => {
      state.session = { ...state.session, ...action.payload };
    },
    
    // Dashboard summary management
    setDashboardSummary: (state, action) => {
      state.dashboardSummary = action.payload;
    },
    
    // Complete reset
    resetChat: (state) => {
      return { ...initialState };
    },
  },
  extraReducers: (builder) => {
    builder
      // Start chat session
      .addCase(startChatSession.pending, (state) => {
        state.ui.isLoading = true;
        state.ui.error = null;
      })
      .addCase(startChatSession.fulfilled, (state, action) => {
        state.ui.isLoading = false;
        state.session.id = action.payload.sessionId;
        state.session.filename = action.payload.filename;
        state.session.isActive = true;
        state.ui.error = null;
      })
      .addCase(startChatSession.rejected, (state, action) => {
        state.ui.isLoading = false;
        state.ui.error = action.payload || CHAT_ERRORS.START_SESSION_FAILED;
      })
      
      // Send chat message
      .addCase(sendChatMessage.pending, (state) => {
        state.ui.isLoading = true;
        state.ui.error = null;
      })
      .addCase(sendChatMessage.fulfilled, (state, action) => {
        state.ui.isLoading = false;
        state.ui.error = null;
        
        const messageContent = action.meta.arg?.message;
        
        // If this is a dashboard summary request, only store it in sidebar (don't add to chat)
        if (messageContent && messageContent.includes(CHAT_MESSAGES.DASHBOARD_SUMMARY_REQUEST)) {
          state.dashboardSummary = extractAnswerFromResponse(action.payload);
        } else {
          // For regular messages, add AI response to chat messages
          const aiMessage = createMessage(
            extractAnswerFromResponse(action.payload),
            MESSAGE_TYPES.AI
          );
          state.messages.push(aiMessage);
        }
      })
      .addCase(sendChatMessage.rejected, (state, action) => {
        state.ui.isLoading = false;
        state.ui.error = action.payload || CHAT_ERRORS.SEND_MESSAGE_FAILED;
        
        // Add error message to chat
        const errorMessage = createMessage(
          CHAT_ERRORS.GENERIC_ERROR,
          MESSAGE_TYPES.AI
        );
        state.messages.push(errorMessage);
      });
  },
});

export const {
  clearError,
  addMessage,
  clearMessages,
  setHasWelcomed,
  setLoading,
  setSession,
  setDashboardSummary,
  resetChat,
} = chatSlice.actions;

export default chatSlice.reducer;