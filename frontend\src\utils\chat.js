/**
 * Chat Utilities
 * Helper functions for chat functionality
 */

import { FILENAME_MAPPING, MESSAGE_TYPES } from "./constants/chat.js";

/**
 * Generate PDF filename based on dashboard configuration
 * @param {string} selectedMonth - Selected month
 * @param {boolean} isFinancialSelected - Financial dashboard selected
 * @param {boolean} isOperationsSelected - Operations dashboard selected
 * @param {boolean} isPayrollSelected - Payroll dashboard selected
 * @param {string} selectedDashboard - Dashboard type (chp/dental)
 * @returns {string} Generated filename
 */
export const generatePdfFilename = (
  selectedMonth,
  isFinancialSelected,
  isOperationsSelected,
  isPayrollSelected,
  selectedDashboard = 'chp'
) => {
  const dashboard = selectedDashboard.toLowerCase();
  
  if (dashboard === 'chp') {
    if (isFinancialSelected) {
      return FILENAME_MAPPING.CHP.FINANCIAL[selectedMonth] || FILENAME_MAPPING.DEFAULT;
    } else if (isOperationsSelected) {
      return FILENAME_MAPPING.CHP.OPERATIONS[selectedMonth] || `CHP Operations Dashboard - ${selectedMonth} 2025.pdf`;
    } else if (isPayrollSelected) {
      return FILENAME_MAPPING.CHP.PAYROLL[selectedMonth] || `CHP Payroll Dashboard - ${selectedMonth} 2025.pdf`;
    }
  } else if (dashboard === 'dental') {
    if (isFinancialSelected) {
      return FILENAME_MAPPING.DENTAL.FINANCIAL[selectedMonth] || `Dental Finance Dashboard - ${selectedMonth} 2025.pdf`;
    } else if (isOperationsSelected) {
      return FILENAME_MAPPING.DENTAL.OPERATIONS[selectedMonth] || `Dental Operations Dashboard - ${selectedMonth} 2025.pdf`;
    } else if (isPayrollSelected) {
      return FILENAME_MAPPING.DENTAL.PAYROLL[selectedMonth] || `Dental Payroll Dashboard - ${selectedMonth} 2025.pdf`;
    }
  }

  return FILENAME_MAPPING.DEFAULT;
};

/**
 * Create a chat message object
 * @param {string} content - Message content
 * @param {string} type - Message type (user/ai/system)
 * @param {string} timestamp - Optional timestamp
 * @returns {Object} Message object
 */
export const createMessage = (content, type = MESSAGE_TYPES.USER, timestamp = null) => ({
  id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${type}`,
  type,
  content,
  timestamp: timestamp || new Date().toLocaleTimeString(),
});

/**
 * Enhance user message with dashboard summary context
 * @param {string} userMessage - Original user message
 * @param {string} dashboardSummary - Dashboard summary context
 * @returns {string} Enhanced message
 */
export const enhanceMessageWithContext = (userMessage, dashboardSummary) => {
  if (!dashboardSummary) return userMessage;
  
  return `Dashboard Summary Context: ${dashboardSummary}\n\nUser Question: ${userMessage}`;
};

/**
 * Extract answer from API response
 * @param {Object} response - API response object
 * @returns {string} Extracted answer
 */
export const extractAnswerFromResponse = (response) => {
  if (!response) return "No response received";
  
  // Try different possible response structures
  const possiblePaths = [
    response.answer,
    response.output,
    response.response,
    response.message,
    response.content,
    response.data?.plainAnswer,
    response.data?.answer,
    response.data?.output,
    response.data?.response,
    response.data?.message,
    response.data?.content,
  ];
  
  const answer = possiblePaths.find(path => path && typeof path === 'string');
  return answer || "No answer received";
};

/**
 * Format welcome message with dynamic content based on selected options
 * @param {string} selectedMonth - Selected month
 * @param {boolean} isFinancialSelected - Financial dashboard selected
 * @param {boolean} isOperationsSelected - Operations dashboard selected
 * @param {boolean} isPayrollSelected - Payroll dashboard selected
 * @param {string} selectedDashboard - Dashboard type (chp/dental)
 * @returns {string} Formatted welcome message
 */
export const formatWelcomeMessage = (
  selectedMonth = 'June',
  isFinancialSelected = true,
  isOperationsSelected = false,
  isPayrollSelected = false,
  selectedDashboard = 'chp'
) => {
  // Determine the selected option
  let selectedOption = '';
  if (isFinancialSelected) {
    selectedOption = 'Financial';
  } else if (isOperationsSelected) {
    selectedOption = 'Operations';
  } else if (isPayrollSelected) {
    selectedOption = 'Payroll';
  }

  // Determine the dashboard type
  const dashboardType = selectedDashboard === 'dental' ? 'Dental' : 'CHP';
  
  // Get current year
  const currentYear = new Date().getFullYear();

  return `Welcome to CFO Dashboard! 👋

I'm analyzing your **${selectedOption} Dashboard** for **${dashboardType} - ${selectedMonth} ${currentYear}**.

How can I help you with this ${selectedOption.toLowerCase()} data?`;
};
