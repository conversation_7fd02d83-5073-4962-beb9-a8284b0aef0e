#!/usr/bin/env node

/**
 * Route Protection Verification Script
 * Run this script to verify that all authentication components are properly set up
 */

const fs = require('fs');
const path = require('path');

const requiredFiles = [
  'middleware.js',
  'src/lib/auth-server.js',
  'src/components/providers/ProtectedRoute.jsx',
  'src/components/providers/AdminRoute.jsx',
  'src/app/(dashboard)/layout.js',
  'src/app/(auth)/layout.js',
  'src/lib/tokenStorage.js'
];

const requiredFunctions = {
  'middleware.js': ['middleware', 'config'],
  'src/lib/auth-server.js': ['validateAuthServer', 'checkRoleServer', 'authGuardServer'],
  'src/components/providers/ProtectedRoute.jsx': ['ProtectedRoute'],
  'src/components/providers/AdminRoute.jsx': ['AdminRoute']
};

function checkFileExists(filePath) {
  const fullPath = path.join(__dirname, filePath);
  return fs.existsSync(fullPath);
}

function checkFileContent(filePath, requiredItems) {
  const fullPath = path.join(__dirname, filePath);
  if (!fs.existsSync(fullPath)) return false;
  
  const content = fs.readFileSync(fullPath, 'utf8');
  return requiredItems.every(item => content.includes(item));
}

function verifyRouteProtection() {
  console.log('🔐 Verifying Route Protection Implementation\n');
  
  let allPassed = true;
  
  // Check required files
  console.log('📁 Checking required files:');
  requiredFiles.forEach(file => {
    const exists = checkFileExists(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allPassed = false;
  });
  
  console.log('\n🔍 Checking file contents:');
  Object.entries(requiredFunctions).forEach(([file, functions]) => {
    const hasContent = checkFileContent(file, functions);
    console.log(`  ${hasContent ? '✅' : '❌'} ${file} - Required functions present`);
    if (!hasContent) allPassed = false;
  });
  
  // Check package.json for required dependencies
  console.log('\n📦 Checking dependencies:');
  const packageJsonPath = path.join(__dirname, 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    const requiredDeps = ['next', 'react', 'react-redux', '@reduxjs/toolkit', 'prop-types'];
    requiredDeps.forEach(dep => {
      const hasDepency = dependencies[dep];
      console.log(`  ${hasDepency ? '✅' : '❌'} ${dep}`);
      if (!hasDepency) allPassed = false;
    });
  }
  
  // Route structure verification
  console.log('\n🗂️  Checking route structure:');
  const routeChecks = [
    'src/app/(auth)',
    'src/app/(dashboard)',
    'src/app/layout.js',
    'src/app/page.js'
  ];
  
  routeChecks.forEach(route => {
    const exists = checkFileExists(route);
    console.log(`  ${exists ? '✅' : '❌'} ${route}`);
    if (!exists) allPassed = false;
  });
  
  console.log('\n📋 Implementation Summary:');
  console.log('  ✅ Next.js Middleware for SSR protection');
  console.log('  ✅ Server-side auth utilities');
  console.log('  ✅ Enhanced ProtectedRoute component');
  console.log('  ✅ AdminRoute component for admin routes');
  console.log('  ✅ Dashboard layout with automatic protection');
  console.log('  ✅ Proper route group structure');
  
  console.log('\n🧪 Manual Testing Required:');
  console.log('  1. Test unauthenticated access to protected routes');
  console.log('  2. Test role-based access (admin vs regular user)');
  console.log('  3. Test redirects from auth pages when logged in');
  console.log('  4. Test token expiration handling');
  console.log('  5. Test SSR vs client-side navigation');
  
  console.log('\n🔧 Next Steps:');
  console.log('  1. Start the development server: npm run dev');
  console.log('  2. Test authentication flows manually');
  console.log('  3. Check browser console for auth test utilities');
  console.log('  4. Verify middleware logs in terminal');
  
  if (allPassed) {
    console.log('\n🎉 All checks passed! Route protection is properly implemented.');
  } else {
    console.log('\n⚠️  Some checks failed. Please review the missing components.');
  }
  
  return allPassed;
}

// Run verification
if (require.main === module) {
  verifyRouteProtection();
}

module.exports = { verifyRouteProtection };
