// Packages
import path from "path";
import fs from "fs/promises";

// Services
import { adpService } from "../services/adp.service.js";

// Utils
import { successResponse, errorResponse } from "../../../../shared/utils/response.util.js";
import * as status from "../../../../shared/utils/status_code.util.js";
import * as constants from "../../../../shared/utils/constants.util.js";

/**
 * Sync Data
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
export const syncData = async (req, res) => {
  try {
    if (!req.file) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CONSTANT_MESSAGES.NO_FILE_UPLOADED));
    }
    const schemaName = req.body.schemaName;

    if (!schemaName) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: constants.CONSTANT_MESSAGES.SCHEMA_NAME_REQUIRED,
      });
    }

    const uploadsDir = path.join(process.cwd(), "uploads");

    await fs.mkdir(uploadsDir, { recursive: true });

    const filePath = path.join(
      uploadsDir,
      Date.now() + "-" + req.file.originalname
    );

    await fs.writeFile(filePath, req.file.buffer);

    const result = await adpService.syncData(filePath, schemaName);

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(constants.CONSTANT_MESSAGES.PAYROLL_DATA_SYNCED, result)
      );
  } catch (error) {
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};
