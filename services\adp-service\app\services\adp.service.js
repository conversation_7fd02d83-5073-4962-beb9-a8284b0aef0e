import path from "path";
import fs from "fs";
import {
  readAndProcessFile,
  extractDatesFromFileName,
  getDynamicColumnMapping,
} from "../utils/excel.util.js";
import { adpRepository } from "../repositories/adp.repository.js";
import { withDb } from "../utils/db.util.js";
import { POSTGRES_CONFIG } from "../../config/db.config.js";

/**
 * ADP Service
 */
export const adpService = {
    syncData: async (filePath, schemaName) => {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found:qqq ${filePath}`);
    }

    const baseName = path.basename(filePath, path.extname(filePath));
    const parts = baseName.split("_");
    const companyName = parts[0];
    const payrollMonth = parts[parts.length - 1];

    const { from_date, to_date } = extractDatesFromFileName(filePath);
    const payrollColumnMapping = getDynamicColumnMapping(filePath);
    return withDb(async (client) => {
      await adpRepository.createTables(
        client,
        payrollColumnMapping,
        schemaName
      );

      const records = readAndProcessFile(
        filePath,
        payrollColumnMapping,
        companyName,
        payrollMonth,
        from_date,
        to_date
      );

      if (!records || records.length === 0) {
        return {
          message: `No records found in file ${filePath}`,
          insertedRecords: 0,
        };
      }

      await adpRepository.insertData(
        client,
        records,
        payrollColumnMapping,
        schemaName
      );

      return {
        message: `Data synced successfully for ${companyName} (${payrollMonth})`,
        insertedRecords: records.length,
      };
    }, POSTGRES_CONFIG);
  },
};
