import { Client } from "pg";
import { Sequelize } from "sequelize";
import { POSTGRES_CONFIG } from "../../config/db.config.js";

let client = null;

// -------------------- pg Client (raw queries) --------------------
/**
 * Connect to PostgreSQL and return the pg client instance
 */
export const connectDb = async () => {
  if (!client) {
    client = new Client(POSTGRES_CONFIG);
    await client.connect();
    console.log("✅ Connected to PostgreSQL (pg)");
  }
  return client;
};

/**
 * Disconnect the pg client
 */
export const disconnectDb = async () => {
  if (client) {
    await client.end();
    console.log("✅ Disconnected from PostgreSQL (pg)");
    client = null;
  }
};

/**
 * Higher-order function to execute queries with automatic connect/disconnect
 * @param {Function} fn - async function that receives the pg client as argument
 */
export const withDb = async (fn) => {
  try {
    const dbClient = await connectDb();
    return await fn(dbClient);
  } finally {
    await disconnectDb();
  }
};

// -------------------- Sequelize instance --------------------
export const sequelize = new Sequelize(
  POSTGRES_CONFIG.database || "perfino-dashboard",
  POSTGRES_CONFIG.user || "postgres",
  POSTGRES_CONFIG.password || "parshwa@1234",
  {
    host: POSTGRES_CONFIG.host || "localhost",
    port: POSTGRES_CONFIG.port || 5432,
    dialect: "postgres",
    logging: console.log,
  }
);

// Test Sequelize connection
sequelize
  .authenticate()
  .then(() => console.log("✅ Connected to PostgreSQL (Sequelize)"))
  .catch((err) => console.error("❌ Sequelize connection error:", err));
