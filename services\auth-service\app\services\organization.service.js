import { v4 as uuidv4 } from "uuid";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { ORGANIZATION_MESSAGES } from "../utils/constants/organization.constants.js";
import { organizationRepository } from "../repository/organization.repository.js";
import { createOrganizationSchemaAndTables } from "./schema.service.js";
import * as status from "../utils/status_code.utils.js";
import { convertToSnakeCase } from "../utils/schema.utils.js";

const logger = createLogger(LOGGER_NAMES.ORGANIZATION_SERVICE);

// HELPER FUNCTIONS
/**
 * Create standardized service response
 */
const createServiceResponse = (
  success,
  statusCode,
  message,
  data = null,
  error = null
) => ({
  success,
  statusCode,
  message,
  data,
  error,
});

/**
 * Validate organization services array
 * @param {Array} services - Array of service strings
 * @returns {boolean} True if valid, throws error if invalid
 */
const validateServices = (services) => {
  const validServices = ["financial", "operational", "payroll"];
  if (!Array.isArray(services)) {
    throw new Error("Services must be an array");
  }
  if (services.length === 0) {
    throw new Error("At least one service must be specified");
  }
  for (const service of services) {
    if (!validServices.includes(service)) {
      throw new Error(
        `Invalid service: ${service}. Must be one of: ${validServices.join(
          ", "
        )}`
      );
    }
  }
  // Remove duplicates
  return [...new Set(services)];
};

// SERVICE FUNCTIONS
/**
 * Add a new organization
 * @param {Object} organizationData - Organization data
 * @param {string} organizationData.name - Organization name
 * @param {string} organizationData.email - Organization email
 * @param {string} organizationData.phone - Organization phone (optional)
 * @param {string} organizationData.website - Organization website (optional)
 * @param {string} organizationData.description - Organization description (optional)
 * @param {Array} organizationData.services - Array of services
 * @param {string} createdBy - ID of user creating the organization (optional)
 * @returns {Promise<Object>} Service response
 */
export const addOrganizationService = async (
  organizationData,
  createdBy = null
) => {
  logger.info(
    "organizationService.addOrganizationService - Starting organization creation"
  );
  try {
    const {
      name,
      email,
      phone,
      website,
      description,
      services,
      realm_id,
      office_id,
    } = organizationData;
    // Validate required fields
    if (!name || !email || !services) {
      logger.warn(
        "organizationService.addOrganizationService - Missing required fields"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.MISSING_REQUIRED_FIELDS,
        null,
        "Name, email, and services are required"
      );
    }
    // Validate and clean services array
    let validatedServices;
    try {
      validatedServices = validateServices(services);
    } catch (error) {
      logger.warn(
        `organizationService.addOrganizationService - Invalid services: ${error.message}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_SERVICES,
        null,
        error.message
      );
    }
    // Check if organization with email already exists
    const existingOrganization =
      await organizationRepository.findOrganizationByEmail(email);
    if (existingOrganization) {
      logger.warn(
        `organizationService.addOrganizationService - Organization already exists with email: ${email}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_CONFLICT,
        ORGANIZATION_MESSAGES.EMAIL_ALREADY_EXISTS,
        null,
        "Organization with this email already exists"
      );
    }
    // Prepare organization data
    const newOrganizationData = {
      id: uuidv4(),
      name: name.trim(),
      email: email.toLowerCase().trim(),
      phone: phone ? phone.trim() : null,
      website: website ? website.trim() : null,
      description: description ? description.trim() : null,
      services: validatedServices,
      created_by: createdBy,
      schema_name: convertToSnakeCase(name.trim()),
      is_deleted: false,
      realm_id: realm_id ? realm_id.trim() : null,
      office_id: office_id ? office_id.trim() : null,
    };
    // Create organization in authentication schema
    const newOrganization = await organizationRepository.createOrganization(
      newOrganizationData
    );
    logger.info(
      `organizationService.addOrganizationService - Organization created successfully with ID: ${newOrganization.id}`
    );
    // Create organization-specific schema and tables
    logger.info(
      `organizationService.addOrganizationService - Creating schema and tables for organization: ${newOrganization.name}`
    );
    let schemaCreationResult = null;
    try {
      schemaCreationResult = await createOrganizationSchemaAndTables(
        newOrganization.name,
        validatedServices
      );
      if (schemaCreationResult.success) {
        logger.info(
          `organizationService.addOrganizationService - Schema and tables created successfully for: ${newOrganization.name}`
        );
      } else {
        logger.warn(
          `organizationService.addOrganizationService - Schema creation failed for: ${newOrganization.name}. Error: ${schemaCreationResult.error}`
        );
      }
    } catch (schemaError) {
      logger.error(
        `organizationService.addOrganizationService - Schema creation error for: ${newOrganization.name}. Error: ${schemaError.message}`
      );
      // Continue with organization creation even if schema creation fails
      schemaCreationResult = {
        success: false,
        error: schemaError.message,
      };
    }
    // Prepare response data
    const responseData = {
      id: newOrganization.id,
      name: newOrganization.name,
      email: newOrganization.email,
      phone: newOrganization.phone,
      website: newOrganization.website,
      description: newOrganization.description,
      services: newOrganization.services,
      created_at: newOrganization.created_at,
      schema_creation: {
        success: schemaCreationResult?.success || false,
        schema_name: schemaCreationResult?.data?.schemaName || null,
        tables_created: schemaCreationResult?.data?.totalTables || 0,
        error: schemaCreationResult?.error || null,
      },
    };
    // Determine overall success message
    let successMessage =
      ORGANIZATION_MESSAGES.ORGANIZATION_CREATED_SUCCESSFULLY;
    if (schemaCreationResult?.success) {
      successMessage = "Organization and schema created successfully";
    } else {
      successMessage =
        "Organization created successfully, but schema creation failed";
    }
    return createServiceResponse(
      true,
      status.STATUS_CODE_CREATED,
      successMessage,
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.addOrganizationService - Error creating organization: ${error.message}`
    );
    // Handle specific database errors
    if (error.name === "SequelizeValidationError") {
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.VALIDATION_ERROR,
        null,
        error.errors.map((err) => err.message).join(", ")
      );
    }
    if (error.name === "SequelizeUniqueConstraintError") {
      return createServiceResponse(
        false,
        status.STATUS_CODE_CONFLICT,
        ORGANIZATION_MESSAGES.EMAIL_ALREADY_EXISTS,
        null,
        "Organization with this email already exists"
      );
    }
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      ORGANIZATION_MESSAGES.ORGANIZATION_CREATION_FAILED,
      null,
      error.message
    );
  }
};

/**
 * Get all organizations (paginated)
 * @param {Object} options { page, limit, filters }
 * @returns {Promise<Object>} Service response
 */
export const getAllOrganizationsService = async (options = {}) => {
  logger.info(
    "organizationService.getAllOrganizationsService - Fetching all organizations"
  );
  try {
    const { page = 1, limit = 10, filters = {} } = options;
    const offset = (page - 1) * limit;
    const repoOptions = {
      where: filters,
      limit,
      offset,
    };
    const organizations = await organizationRepository.findAllOrganizations(
      repoOptions
    );
    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      ORGANIZATION_MESSAGES.ORGANIZATIONS_FETCHED_SUCCESSFULLY ||
        "Organizations fetched successfully",
      organizations
    );
  } catch (error) {
    logger.error(
      `organizationService.getAllOrganizationsService - Error: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      ORGANIZATION_MESSAGES.ORGANIZATIONS_FETCH_FAILED ||
        "Failed to fetch organizations",
      null,
      error.message
    );
  }
};

/**
 * Get organization by ID
 * @param {string} organizationId - Organization ID (UUID)
 * @returns {Promise<Object>} Service response
 */
export const getOrganizationService = async (organizationId) => {
  logger.info(
    `organizationService.getOrganizationService - Fetching organization with ID: ${organizationId}`
  );
  try {
    // Validate organization ID
    if (!organizationId) {
      logger.warn(
        "organizationService.getOrganizationService - Missing organization ID"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Organization ID is required"
      );
    }

    // Validate UUID format (basic check)
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(organizationId)) {
      logger.warn(
        `organizationService.getOrganizationService - Invalid organization ID format: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Invalid organization ID format"
      );
    }

    // Fetch organization from repository
    const organization = await organizationRepository.findOrganizationById(
      organizationId
    );

    if (!organization) {
      logger.warn(
        `organizationService.getOrganizationService - Organization not found with ID: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
        null,
        "Organization not found"
      );
    }

    logger.info(
      `organizationService.getOrganizationService - Organization found successfully: ${organization.name}`
    );

    // Prepare response data
    const responseData = {
      id: organization.id,
      name: organization.name,
      email: organization.email,
      phone: organization.phone,
      website: organization.website,
      description: organization.description,
      services: organization.services,
      is_active: organization.is_active,
      schema_name: organization.schema_name,
      created_at: organization.created_at,
      updated_at: organization.updated_at,
      created_by: organization.created_by,
    };

    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      ORGANIZATION_MESSAGES.ORGANIZATION_FOUND,
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.getOrganizationService - Error fetching organization: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
      null,
      error.message
    );
  }
};

/**
 * Get organization by office_id
 * @param {string} officeId - Organization office_id
 * @returns {Promise<Object>} Service response
 */
export const getOrganizationByOfficeIdService = async (officeId) => {
  logger.info(
    `organizationService.getOrganizationByOfficeIdService - Fetching organization with office_id: ${officeId}`
  );
  try {
    // Validate office_id
    if (!officeId) {
      logger.warn(
        "organizationService.getOrganizationByOfficeIdService - Missing office_id"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Office ID is required"
      );
    }

    // Fetch organization from repository
    const organization =
      await organizationRepository.findOrganizationByOfficeId(officeId);

    if (!organization) {
      logger.warn(
        `organizationService.getOrganizationByOfficeIdService - Organization not found with office_id: ${officeId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
        null,
        "Organization not found"
      );
    }

    logger.info(
      `organizationService.getOrganizationByOfficeIdService - Organization found successfully: ${organization.name}`
    );

    // Prepare response data
    const responseData = {
      id: organization.id,
      name: organization.name,
      email: organization.email,
      phone: organization.phone,
      website: organization.website,
      description: organization.description,
      services: organization.services,
      is_active: organization.is_active,
      schema_name: organization.schema_name,
      office_id: organization.office_id,
      realm_id: organization.realm_id,
      created_at: organization.created_at,
      updated_at: organization.updated_at,
      created_by: organization.created_by,
    };

    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      ORGANIZATION_MESSAGES.ORGANIZATION_FOUND,
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.getOrganizationByOfficeIdService - Error fetching organization: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
      null,
      error.message
    );
  }
};

/**
 * Get organization by realm_id
 * @param {string} realmId - Organization realm_id
 * @returns {Promise<Object>} Service response
 */
export const getOrganizationByRealmIdService = async (realmId) => {
  logger.info(
    `organizationService.getOrganizationByRealmIdService - Fetching organization with realm_id: ${realmId}`
  );
  try {
    // Validate realm_id
    if (!realmId) {
      logger.warn(
        "organizationService.getOrganizationByRealmIdService - Missing realm_id"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Realm ID is required"
      );
    }

    // Fetch organization from repository
    const organization = await organizationRepository.findOrganizationByRealmId(
      realmId
    );

    if (!organization) {
      logger.warn(
        `organizationService.getOrganizationByRealmIdService - Organization not found with realm_id: ${realmId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
        null,
        "Organization not found"
      );
    }

    logger.info(
      `organizationService.getOrganizationByRealmIdService - Organization found successfully: ${organization.name}`
    );

    // Prepare response data
    const responseData = {
      id: organization.id,
      name: organization.name,
      email: organization.email,
      phone: organization.phone,
      website: organization.website,
      description: organization.description,
      services: organization.services,
      is_active: organization.is_active,
      schema_name: organization.schema_name,
      office_id: organization.office_id,
      realm_id: organization.realm_id,
      created_at: organization.created_at,
      updated_at: organization.updated_at,
      created_by: organization.created_by,
    };

    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      ORGANIZATION_MESSAGES.ORGANIZATION_FOUND,
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.getOrganizationByRealmIdService - Error fetching organization: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
      null,
      error.message
    );
  }
};

/**
 * Update sync field for organization
 * @param {string} organizationId - Organization ID
 * @param {string} syncType - Type of sync (qb, sikka, adp)
 * @returns {Promise<Object>} Service response
 */
export const updateSyncFieldService = async (organizationId, syncType) => {
  logger.info(
    `organizationService.updateSyncFieldService - Updating sync field for organization: ${organizationId}, type: ${syncType}`
  );
  try {
    // Validate organization ID
    if (!organizationId) {
      logger.warn(
        "organizationService.updateSyncFieldService - Missing organization ID"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Organization ID is required"
      );
    }

    // Validate sync type
    const validSyncTypes = ["qb", "sikka", "adp"];
    if (!syncType || !validSyncTypes.includes(syncType)) {
      logger.warn(
        `organizationService.updateSyncFieldService - Invalid sync type: ${syncType}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        "Invalid sync type",
        null,
        `Sync type must be one of: ${validSyncTypes.join(", ")}`
      );
    }

    // Validate UUID format (basic check)
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(organizationId)) {
      logger.warn(
        `organizationService.updateSyncFieldService - Invalid organization ID format: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Invalid organization ID format"
      );
    }

    // Check if organization exists
    const existingOrganization =
      await organizationRepository.findOrganizationById(organizationId);
    if (!existingOrganization) {
      logger.warn(
        `organizationService.updateSyncFieldService - Organization not found with ID: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
        null,
        "Organization not found"
      );
    }

    // Update sync field
    const updatedOrganization = await organizationRepository.updateSyncField(
      organizationId,
      syncType
    );

    if (!updatedOrganization) {
      logger.warn(
        `organizationService.updateSyncFieldService - Failed to update sync field for organization: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        "Failed to update sync field",
        null,
        "Database update failed"
      );
    }

    logger.info(
      `organizationService.updateSyncFieldService - Sync field updated successfully for organization: ${organizationId}`
    );

    // Prepare response data
    const responseData = {
      id: updatedOrganization.id,
      name: updatedOrganization.name,
      email: updatedOrganization.email,
      qb_last_synced_at: updatedOrganization.qb_last_synced_at,
      sikka_last_synced_at: updatedOrganization.sikka_last_synced_at,
      adp_last_synced_at: updatedOrganization.adp_last_synced_at,
      updated_at: updatedOrganization.updated_at,
    };

    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      "Sync field updated successfully",
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.updateSyncFieldService - Error updating sync field: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      "Failed to update sync field",
      null,
      error.message
    );
  }
};

/**
 * Update realm_id for organization
 * @param {string} organizationId - Organization ID
 * @param {string} realmId - New realm_id value
 * @returns {Promise<Object>} Service response
 */
export const updateRealmIdService = async (organizationId, realmId) => {
  logger.info(
    `organizationService.updateRealmIdService - Updating realm_id for organization: ${organizationId}, realm_id: ${realmId}`
  );
  try {
    // Validate organization ID
    if (!organizationId) {
      logger.warn(
        "organizationService.updateRealmIdService - Missing organization ID"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Organization ID is required"
      );
    }

    // Validate realm_id
    if (
      !realmId ||
      typeof realmId !== "string" ||
      realmId.trim().length === 0
    ) {
      logger.warn(
        `organizationService.updateRealmIdService - Invalid realm_id: ${realmId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        "Invalid realm_id",
        null,
        "Realm ID must be a non-empty string"
      );
    }

    // Validate UUID format (basic check)
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(organizationId)) {
      logger.warn(
        `organizationService.updateRealmIdService - Invalid organization ID format: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Invalid organization ID format"
      );
    }

    // Check if organization exists
    const existingOrganization =
      await organizationRepository.findOrganizationById(organizationId);
    if (!existingOrganization) {
      logger.warn(
        `organizationService.updateRealmIdService - Organization not found with ID: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
        null,
        "Organization not found"
      );
    }

    // Update realm_id
    const updatedOrganization = await organizationRepository.updateRealmId(
      organizationId,
      realmId.trim()
    );

    if (!updatedOrganization) {
      logger.warn(
        `organizationService.updateRealmIdService - Failed to update realm_id for organization: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        "Failed to update realm_id",
        null,
        "Database update failed"
      );
    }

    logger.info(
      `organizationService.updateRealmIdService - Realm ID updated successfully for organization: ${organizationId}`
    );

    // Prepare response data
    const responseData = {
      id: updatedOrganization.id,
      name: updatedOrganization.name,
      email: updatedOrganization.email,
      realm_id: updatedOrganization.realm_id,
      updated_at: updatedOrganization.updated_at,
    };

    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      "Realm ID updated successfully",
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.updateRealmIdService - Error updating realm_id: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      "Failed to update realm_id",
      null,
      error.message
    );
  }
};

/**
 * Check if realm_id exists for organization
 * @param {string} organizationId - Organization ID
 * @returns {Promise<Object>} Service response with exists flag
 */
export const checkRealmIdExistsService = async (organizationId) => {
  logger.info(
    `organizationService.checkRealmIdExistsService - Checking realm_id for organization: ${organizationId}`
  );
  try {
    // Validate organization ID
    if (!organizationId) {
      logger.warn(
        "organizationService.checkRealmIdExistsService - Missing organization ID"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Organization ID is required"
      );
    }

    // Validate UUID format (basic check)
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(organizationId)) {
      logger.warn(
        `organizationService.checkRealmIdExistsService - Invalid organization ID format: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Invalid organization ID format"
      );
    }

    // Fetch organization from repository
    const organization = await organizationRepository.findOrganizationById(
      organizationId
    );

    if (!organization) {
      logger.warn(
        `organizationService.checkRealmIdExistsService - Organization not found with ID: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
        null,
        "Organization not found"
      );
    }

    // Check if realm_id exists
    const realmIdExists = !!organization.realm_id;

    logger.info(
      `organizationService.checkRealmIdExistsService - Realm ID ${
        realmIdExists ? "exists" : "does not exist"
      } for organization: ${organizationId}`
    );

    // Prepare response data
    const responseData = {
      organization_id: organization.id,
      realm_id: organization.realm_id,
      exists: realmIdExists,
    };

    const message = realmIdExists
      ? "Realm ID exists for organization"
      : "Realm ID does not exist for organization";

    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      message,
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.checkRealmIdExistsService - Error checking realm_id: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      "Failed to check realm_id",
      null,
      error.message
    );
  }
};

/**
 * Check if realm_id is associated with any organization
 * @param {string} realmId - Realm ID to check
 * @returns {Promise<Object>} Service response with association status
 */
export const checkRealmIdAssociationService = async (realmId) => {
  logger.info(
    `organizationService.checkRealmIdAssociationService - Checking realm_id association: ${realmId}`
  );
  try {
    // Validate realm_id
    if (!realmId) {
      logger.warn(
        "organizationService.checkRealmIdAssociationService - Missing realm_id"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        "Realm ID is required",
        null,
        "Realm ID parameter is missing"
      );
    }

    // Fetch organization from repository by realm_id
    const organization = await organizationRepository.findOrganizationByRealmId(
      realmId
    );

    // Prepare response data
    const responseData = {
      realm_id: realmId,
      is_associated: !!organization,
      org_name: organization?.name || null,
    };

    const message = organization
      ? `Realm ID is associated with organization: ${organization.name}`
      : "Realm ID is not associated with any organization";

    logger.info(
      `organizationService.checkRealmIdAssociationService - Realm ID ${
        organization ? "is associated" : "is not associated"
      } with any organization`
    );

    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      message,
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.checkRealmIdAssociationService - Error checking realm_id association: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      "Failed to check realm_id association",
      null,
      error.message
    );
  }
};

/**
 * Update QB connection status for organization
 * @param {string} organizationId - Organization ID
 * @returns {Promise<Object>} Service response
 */
export const updateQbConnectionService = async (organizationId) => {
  logger.info(
    `organizationService.updateQbConnectionService - Updating QB connection status for organization: ${organizationId}`
  );
  try {
    // Validate organization ID
    if (!organizationId) {
      logger.warn(
        "organizationService.updateQbConnectionService - Missing organization ID"
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Organization ID is required"
      );
    }

    // Validate UUID format (basic check)
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(organizationId)) {
      logger.warn(
        `organizationService.updateQbConnectionService - Invalid organization ID format: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        ORGANIZATION_MESSAGES.INVALID_ORGANIZATION_DATA,
        null,
        "Invalid organization ID format"
      );
    }

    // Check if organization exists
    const existingOrganization =
      await organizationRepository.findOrganizationById(organizationId);
    if (!existingOrganization) {
      logger.warn(
        `organizationService.updateQbConnectionService - Organization not found with ID: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        ORGANIZATION_MESSAGES.ORGANIZATION_NOT_FOUND,
        null,
        "Organization not found"
      );
    }

    // Update QB connection status to true
    const updatedOrganization = await organizationRepository.updateQbConnection(
      organizationId,
      true
    );

    if (!updatedOrganization) {
      logger.warn(
        `organizationService.updateQbConnectionService - Failed to update QB connection status for organization: ${organizationId}`
      );
      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        "Failed to update QB connection status",
        null,
        "Database update failed"
      );
    }

    logger.info(
      `organizationService.updateQbConnectionService - QB connection status updated successfully for organization: ${organizationId}`
    );

    // Prepare response data
    const responseData = {
      id: updatedOrganization.id,
      name: updatedOrganization.name,
      email: updatedOrganization.email,
      is_qb_connected: updatedOrganization.is_qb_connected,
      updated_at: updatedOrganization.updated_at,
    };

    return createServiceResponse(
      true,
      status.STATUS_CODE_OK,
      "QB connection status updated successfully",
      responseData
    );
  } catch (error) {
    logger.error(
      `organizationService.updateQbConnectionService - Error updating QB connection status: ${error.message}`
    );
    return createServiceResponse(
      false,
      status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      "Failed to update QB connection status",
      null,
      error.message
    );
  }
};
