"use strict";

const bcrypt = require("bcryptjs");
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Hash the password using bcrypt
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash("password123", saltRounds);

    // Generate UUIDs for the demo data
    const userId = uuidv4();
    const tenantId = uuidv4(); // Using a random UUID for tenant_id

    // Insert a demo user only
    await queryInterface.bulkInsert(
      "app_user",
      [
        {
          id: userId,
          tenant_id: tenantId,
          email: "<EMAIL>",
          password_hash: hashedPassword,
          full_name: "Demo User",
          phone_number: "******-0123",
          roles: ["admin", "user"],
          email_verified: true,
          email_verified_at: new Date(),
          last_login: null,
          mfa_enabled: false,
          mfa_secret: null,
          mfa_backup_codes: null,
          invited_by: null,
          invited_at: null,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ],
      {
        schema: "Authentication",
      }
    );

    console.log("✅ Demo user seeded successfully");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: password123");
  },

  down: async (queryInterface, Sequelize) => {
    // Remove the demo user
    await queryInterface.bulkDelete(
      "app_user",
      {
        email: "<EMAIL>",
      },
      {
        schema: "Authentication",
      }
    );

    console.log("✅ Demo user removed successfully");
  },
};
