// app/controllers/chat.controller.js
import { startChat, sendMessage } from "../services/chat.service.js";

/**
 * POST /api/chat/start
 * Starts a chat session for a selected document.
 */
export const handleStartChat = async (req, res) => {
  try {
    const { filename } = req.body;
    if (!filename)
      return res.status(400).json({ error: "filename is required" });

    const session = await startChat(filename);
    res.json(session);
  } catch (err) {
    console.error("handleStartChat error:", err);
    res.status(500).json({ error: err.message || "Failed to start chat" });
  }
};

/**
 * POST /api/chat/message
 * Sends user's message + organization (optional) + summary flag to FinChat.
 * @param {string} sessionId - Chat session ID
 * @param {string} message - User's message/question
 * @param {string} [organization] - Organization name for context
 * @param {boolean} [summary] - If true, use summary prompt; if false, use conversational chat prompt
 */
export const handleChatMessage = async (req, res) => {
  try {
    const { sessionId, message, organization, summary = false } = req.body;

    if (!sessionId || !message) {
      return res
        .status(400)
        .json({ error: "sessionId and message are required" });
    }

    // Convert summary to boolean if it's a string
    const isSummaryMode = summary === true || summary === "true" || summary === 1;

    // organization and summary flag passed from frontend
    const result = await sendMessage(sessionId, message, organization, isSummaryMode);
    res.json({
      plainAnswer: result.plainAnswer,
      filename: result.filename,
    });
  } catch (err) {
    console.error("handleChatMessage error:", err);
    res.status(500).json({ error: err.message || "Failed to send message" });
  }
};
