// app/services/chat.service.js
import crypto from "crypto";
import { chatWithContext } from "../utils/azureOpenAI.js";
import { downloadPdfBlob } from "../utils/blobClient.js";
import { extractTextFromPdfBuffer } from "../utils/pdfLoader.js";
import {
  createSession,
  getSession,
  appendHistory,
  getCachedDocText,
  setCachedDocText,
} from "../utils/memoryStore.js";
import { searchCompetitorData } from "../utils/azureSearchClient.js";

/**
 * Get document text (with caching).
 * Downloads PDF from Azure Blob Storage and extracts text.
 */
async function getDocumentText(filename) {
  const cached = getCachedDocText(filename);
  if (cached) return cached;

  const buffer = await downloadPdfBlob(filename);
  const text = await extractTextFromPdfBuffer(buffer);

  setCachedDocText(filename, text);
  return text;
}

/**
 * Start chat session tied to a specific document.
 */
export async function startChat(filename) {
  if (!filename) throw new Error("filename is required");
  await getDocumentText(filename);
  const sessionId = crypto.randomUUID();
  createSession({ sessionId, filename });
  return { sessionId, filename };
}

/**
 * Send message — handles competitor comparison & organization fallback.
 * @param {string} sessionId - Chat session ID
 * @param {string} userMessage - User's message/question
 * @param {string} [organization] - Organization name for context
 * @param {boolean} [summaryMode=false] - If true, use summary prompt; if false, use conversational chat prompt
 */
export async function sendMessage(sessionId, userMessage, organization, summaryMode = false) {
  const session = getSession(sessionId);
  if (!session) throw new Error("Invalid sessionId");

  const { filename, history } = session;
  const docText = await getDocumentText(filename);

  // ✅ 1️⃣ Clarify organization's role in the question (only for summary mode)
  let processedMessage = userMessage;
  if (summaryMode && organization && !new RegExp(organization, "i").test(userMessage)) {
    processedMessage = `${userMessage}. Analyze the performance of ${organization} based on the provided financial document, and compare it with other relevant competitors in the same industry.`;
    console.log(`Enhanced message with org context: ${processedMessage}`);
  }

  // ✅ 2️⃣ Detect competitor-related intent (only for summary mode or if explicitly requested)
  let competitorData = [];
  let competitorName = null;

  if (summaryMode) {
    const isCompetitorQuery = /(compare|vs|versus|competitor|benchmark|against)/i.test(
      processedMessage
    );

    if (isCompetitorQuery) {
      const match = processedMessage.match(/vs\s+([\w\s&]+)/i);
      competitorName = match ? match[1].trim() : null;

      // 🧠 If no explicit competitor, auto-detect using Azure Search
      if (!competitorName && organization) {
        console.log(`No competitor name found — searching competitors for ${organization}`);
        competitorData = await searchCompetitorData(organization);
      } else if (competitorName) {
        console.log(`Searching competitor data for: ${competitorName}`);
        competitorData = await searchCompetitorData(competitorName);
      }
    } else if (organization) {
      // Even if user didn't say "compare", try fetching top peers for summary mode
      competitorData = await searchCompetitorData(organization);
    }
  }

  // ✅ 3️⃣ Merge document + competitor data
  const competitorBlock = competitorData.length
    ? `--- COMPETITOR DATA (${competitorData.length} results) ---\n${competitorData
        .map(
          (s) =>
            `${s.title}\n${s.snippet}\n(Source: ${s.url || "public source"})`
        )
        .join("\n\n")}`
    : "";

  const fullContext = `--- DOCUMENT CONTEXT ---\n${docText}\n\n${competitorBlock}`;

  // ✅ 4️⃣ Send to Azure OpenAI with summary mode flag
  const answer = await chatWithContext({
    contextText: fullContext,
    userQuestion: processedMessage,
    history,
    summaryMode: summaryMode,
  });

  appendHistory(sessionId, "user", processedMessage);
  appendHistory(sessionId, "assistant", answer);

  return { plainAnswer: sanitizeToPlain(answer), filename };
}

/** Clean text output for display */
function sanitizeToPlain(text) {
  if (!text) return "";
  let s = String(text);
  s = s.replace(/```[\s\S]*?```/g, " ");
  s = s.replace(/`([^`]+)`/g, "$1");
  s = s.replace(/[*_]+/g, "");
  s = s.replace(/\r\n/g, "\n");
  s = s.replace(/\n{3,}/g, "\n\n");
  s = s.replace(/\s{2,}/g, " ").trim();
  return s;
}
