

// app/utils/azureOpenAI.js
import dotenv from "dotenv";
dotenv.config();

import OpenAI from "openai";
import { SUMMARY_SYSTEM_PROMPT } from "./prompts/summary.prompt.js";
import { CHAT_SYSTEM_PROMPT } from "./prompts/chat.prompt.js";

/**
 * Azure OpenAI client configuration
 * Uses environment variables for endpoint, deployment name, API key, and version.
 * Lazy-loaded to ensure environment variables are available.
 */
let client = null;

function getClient() {
  if (!client) {
    const apiKey = process.env.AZURE_OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error("AZURE_OPENAI_API_KEY environment variable is missing or empty");
    }
    
    client = new OpenAI({
      apiKey: apiKey,
      baseURL: `${process.env.AZURE_OPENAI_ENDPOINT}openai/deployments/${process.env.AZURE_OPENAI_DEPLOYMENT_NAME}`,
      defaultQuery: { "api-version": process.env.AZURE_OPENAI_API_VERSION },
      defaultHeaders: { "api-key": apiKey }
    });
  }
  return client;
}

/**
 * Core chat handler for FinChat Assistant.
 * Provides conversational, financial-analysis-style answers using only the provided context.
 * @param {string} contextText - Document/context text
 * @param {string} userQuestion - User's question
 * @param {Array} history - Chat history
 * @param {number} maxTokens - Maximum tokens
 * @param {boolean} summaryMode - If true, use summary prompt; if false, use conversational chat prompt
 */
export async function chatWithContext({
  contextText,
  userQuestion,
  history = [],
  maxTokens = 3000,
  summaryMode = false
}) {

  // Select prompt based on summary mode
  const system = summaryMode ? SUMMARY_SYSTEM_PROMPT : CHAT_SYSTEM_PROMPT;




const contextBlock = `---DOCUMENT CONTEXT START---
${contextText}
---DOCUMENT CONTEXT END---`;

  const messages = [
    { role: "system", content: system },
    { role: "user", content: `${contextBlock}` },
    ...history, // optional chat memory [{role:'user'|'assistant', content:'...'}]
    { role: "user", content: `Question: ${userQuestion}` }
  ];

  const openAIClient = getClient();
  const res = await openAIClient.chat.completions.create({
    model: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
    messages,
    max_tokens: maxTokens,
    temperature: 0.2
  });

  return res.choices?.[0]?.message?.content?.trim() || "No response generated.";
}

/**
 * Helper to summarize or extract insights from a document
 */
export async function chatWithDocument(documentText, instructionPrompt, opts = {}) {
  const question = instructionPrompt || "Provide a concise, helpful summary.";
  return await chatWithContext({
    contextText: documentText,
    userQuestion: question,
    history: [],
    maxTokens: opts.maxTokens || 3000
  });
}
