// app/utils/azureSearchClient.js
import dotenv from "dotenv";
dotenv.config();

import fetch from "node-fetch";

export async function searchCompetitorData(query) {
  const endpoint = process.env.AZURE_SEARCH_ENDPOINT;
  const key = process.env.AZURE_SEARCH_API_KEY;
  const index = process.env.AZURE_SEARCH_INDEX;
  if (!endpoint || !key || !index) {
    console.warn("Azure Search config missing. Skipping competitor fetch.");
    return [];
  }

  const url = `${endpoint}/indexes/${index}/docs/search?api-version=2023-07-01-Preview`;
  const body = {
    search: query,
    top: 3,
    select: "title,url,snippet"
  };

  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "api-key": key
    },
    body: JSON.stringify(body)
  });

  const data = await res.json();
  if (!data.value) return [];

  return data.value.map(doc => ({
    title: doc.title,
    url: doc.url,
    snippet: doc.snippet
  }));
}
