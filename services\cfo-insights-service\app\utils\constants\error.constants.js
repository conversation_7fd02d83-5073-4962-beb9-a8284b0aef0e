export const SERVER_ERROR_MESSAGES = {
  INTERNAL_SERVER_ERROR: "Internal server error",
  ROUTE_NOT_FOUND: "Route not found",
  ORIGIN_NOT_ALLOWED: "Origin not allowed by CORS policy",
  CORS_POLICY_VIOLATION: "CORS policy violation",
  DATABASE_CONNECTION_FAILED: "Database connection failed",
  SERVICE_UNAVAILABLE: "Service temporarily unavailable",
  INVALID_REQUEST: "Invalid request",
  UNAUTHORIZED: "Unauthorized access",
  FORBIDDEN: "Forbidden access",
  VALIDATION_ERROR: "Validation error",
  RATE_LIMIT_EXCEEDED: "Rate limit exceeded",
  FILE_TOO_LARGE: "File too large",
  UNSUPPORTED_FILE_TYPE: "Unsupported file type",
  PROCESSING_ERROR: "Error processing request",
  PINECONE_CONNECTION_FAILED: "Pinecone connection failed",
  AZURE_CONNECTION_FAILED: "Azure connection failed",
  DOCUMENT_PROCESSING_FAILED: "Document processing failed",
  VECTOR_SEARCH_FAILED: "Vector search failed",
  INSIGHTS_GENERATION_FAILED: "Insights generation failed",
};

export const CLIENT_ERROR_MESSAGES = {
  BAD_REQUEST: "Bad request",
  UNAUTHORIZED: "Unauthorized",
  FORBIDDEN: "Forbidden",
  NOT_FOUND: "Not found",
  CONFLICT: "Conflict",
  UNPROCESSABLE_ENTITY: "Unprocessable entity",
  TOO_MANY_REQUESTS: "Too many requests",
  PAYLOAD_TOO_LARGE: "Payload too large",
  UNSUPPORTED_MEDIA_TYPE: "Unsupported media type",
};

export const VALIDATION_ERROR_MESSAGES = {
  REQUIRED_FIELD: "This field is required",
  INVALID_EMAIL: "Invalid email format",
  INVALID_URL: "Invalid URL format",
  INVALID_UUID: "Invalid UUID format",
  INVALID_DATE: "Invalid date format",
  INVALID_NUMBER: "Invalid number format",
  INVALID_BOOLEAN: "Invalid boolean value",
  INVALID_ARRAY: "Invalid array format",
  INVALID_OBJECT: "Invalid object format",
  MIN_LENGTH: "Minimum length not met",
  MAX_LENGTH: "Maximum length exceeded",
  MIN_VALUE: "Minimum value not met",
  MAX_VALUE: "Maximum value exceeded",
  INVALID_FILE_TYPE: "Invalid file type",
  FILE_SIZE_EXCEEDED: "File size exceeded",
  INVALID_QUERY_PARAMS: "Invalid query parameters",
  MISSING_HEADERS: "Required headers missing",
  INVALID_CONTENT_TYPE: "Invalid content type",
};
