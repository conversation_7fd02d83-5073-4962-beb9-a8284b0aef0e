export const SERVICE_CONFIG = {
  NAME: "CFO Insights Service",
  VERSION: "1.0.0",
  DEFAULT_PORT: 3007,
  DEFAULT_ENV: "development",
};

export const CORS_CONFIG = {
  ALLOWED_DOMAIN: "localhost",
  METHODS: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  ALLOWED_HEADERS: [
    "Origin",
    "X-Requested-With",
    "Content-Type",
    "Accept",
    "Authorization",
    "Cache-Control",
    "Pragma",
  ],
  EXPOSED_HEADERS: ["X-Total-Count", "X-Page-Count"],
  CREDENTIALS: true,
  MAX_AGE: 86400, // 24 hours
  PREFLIGHT_CONTINUE: false,
  OPTIONS_SUCCESS_STATUS: 204,
};

export const REQUEST_CONFIG = {
  JSON_LIMIT: "10mb",
  URL_ENCODED_LIMIT: "10mb",
};

export const HEALTH_MESSAGES = {
  SERVICE_HEALTHY: "CFO Insights Service is healthy",
  DATABASE_CONNECTION_FAILED: "Pinecone connection failed",
  HEALTH_CHECK_FAILED: "Health check failed",
};

export const WELCOME_MESSAGES = {
  SERVICE_WELCOME: "Welcome to CFO Insights Service API",
};

export const AVAILABLE_ENDPOINTS = [
  "GET /health",
  "GET /",
  "POST /api/chat/start",
  "POST /api/chat/message",
];

export const PROCESS_SIGNALS = {
  SIGTERM: "SIGTERM",
  SIGINT: "SIGINT",
};

export const SERVER_ERROR_CODES = {
  EADDRINUSE: "EADDRINUSE",
};

export const ENV_VALUES = {
  DEVELOPMENT: "development",
  PRODUCTION: "production",
  TEST: "test",
};
