import dotenv from "dotenv";
dotenv.config();

import express from "express";
import bodyParser from "body-parser";
import cors from "cors";

import mainRouter from "./app/routes/index.js";

console.log("📝 Starting CFO Insights Service...");

const app = express();

// ✅ Middleware setup

// ✅ Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: "10mb" }));

// ✅ API routes
// ✅ API routes
app.use("/api", mainRouter);

// ✅ Default route
app.get("/", (_req, res) => {
  res.send("CFO Insights Chat Service is running 🚀");
});
// ✅ Default route
app.get("/", (_req, res) => {
  res.send("CFO Insights Chat Service is running 🚀");
});

// ✅ Port setup — will use 3009 by default
const PORT = process.env.CFO_INSIGHTS_SERVICE_PORT || 3007;

// ✅ Start the server
app.listen(PORT, () => {
  console.log(`✅ CFO Insights Service is running on port ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}`);
  console.log(`📊 API base: http://localhost:${PORT}/api`);
});
