import {
  QUICKBOOKS_CONTROLLER_LOGS,
  QUICKBOOKS_ERROR_LOGS,
  QUICKBOOKS_SUCCESS_LOGS,
} from "../utils/constants/log.constants.js";
import {
  QUICKBOOKS_DEFAULTS,
  QUICKBOOKS_STATUS,
  QUICKBOOKS_FIELD_NAMES,
  QUICKBOOKS_VALIDATION,
  QUICKBOOKS_LOGGER_NAMES,
} from "../utils/constants/config.constants.js";
import { QUICKBOOKS_MESSAGES } from "../utils/constants/error.constants.js";
import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import * as status from "../utils/status_code.utils.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import { createLogger } from "../utils/logger.utils.js";
import { encrypt } from "../utils/encryption.utils.js";
import quickbooksService from "../services/quickbooks.service.js";
import { handleOrganizationRealmId } from "../services/quickbooks.service.js";
import quickbooksRepository from "../repositories/quickbooks.repository.js";
import { processReport } from "./reports.controller.js";
import { ORG_API_BASE_URL, updateOrganizationQbConnection } from "../utils/organization-api.util.js";
import axios from "axios";

const logger = createLogger(QUICKBOOKS_LOGGER_NAMES.QUICKBOOKS_CONTROLLER);

/**
 * Fetch organization schema name by realm_id
 * @param {string} realmId - Realm ID
 * @returns {Promise<string|null>} Schema name or null if not found
 */
const fetchOrganizationSchemaName = async (realmId) => {
  try {
    const orgApiUrl = `http://localhost:3001/api/organization/realm/${realmId}`;

    logger.info(`Fetching organization details for realm_id: ${realmId}`);
    const response = await axios.get(orgApiUrl);

    if (response.status === 200 && response.data) {
      const schemaName = response.data.data?.schema_name;

      logger.info(
        `Retrieved schema name: ${schemaName} for realm_id: ${realmId}`
      );
      return schemaName;
    } else {
      logger.warn(
        `Failed to retrieve organization details for realm_id: ${realmId}`
      );
      return null;
    }
  } catch (error) {
    logger.error(
      `Error fetching organization details for realm_id ${realmId}:`,
      { error: error.message }
    );
    return null;
  }
};

// Environment Configuration - Centralized and validated
const QB_CONFIG = {
  clientID: process.env.QUICKBOOKS_CLIENT_ID,
  clientSecret: process.env.QUICKBOOKS_CLIENT_SECRET,
  tokenUrl: process.env.QUICKBOOKS_TOKEN_URL,
  redirectUri: process.env.QUICKBOOKS_REDIRECT_URI,
};

// Validation helper for environment variables
const validateEnvironmentConfig = () => {
  const missingVars = Object.entries(QB_CONFIG)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingVars.length > 0) {
    throw new Error(
      `${HARDCODED_STRINGS.MISSING_ENV_VARS}: ${missingVars.join(", ")}`
    );
  }
};

// Initialize and validate configuration
try {
  validateEnvironmentConfig();
} catch (error) {
  logger.error(QUICKBOOKS_CONTROLLER_LOGS.CONFIG_ERROR, {
    error: error.message,
  });
}

/**
 * Validates required fields for API requests
 * @param {Object} data - Request data
 * @param {Array} requiredFields - Array of required field names
 * @returns {Array} Array of missing fields
 */
const validateRequiredFields = (data, requiredFields) => {
  try {
    return requiredFields.filter((field) => !data[field]);
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.VALIDATE_REQUIRED_FIELDS_ERROR, {
      error: error.message,
      stack: error.stack,
      data: data,
      requiredFields: requiredFields,
    });
    throw error;
  }
};

const handleDatabaseError = (error) => {
  try {
    const errorMappings = {
      [QUICKBOOKS_DEFAULTS.SQL_ERROR_FOREIGN_KEY]: {
        status: status.STATUS_CODE_BAD_REQUEST,
        message: QUICKBOOKS_MESSAGES.FOREIGN_KEY_VIOLATION_ERROR,
      },
      [QUICKBOOKS_DEFAULTS.SQL_ERROR_DUPLICATE]: {
        status: status.STATUS_CODE_BAD_REQUEST,
        message: QUICKBOOKS_MESSAGES.DUPLICATE_QUICKBOOK_ACCOUNT_ERROR,
      },
      [QUICKBOOKS_DEFAULTS.POSTGRES_UNIQUE_VIOLATION]: {
        status: status.STATUS_CODE_CONFLICT,
        message: QUICKBOOKS_MESSAGES.DUPLICATE_QUICKBOOK_ACCOUNT_ERROR,
      },
    };

    return (
      errorMappings[error.number] ||
      errorMappings[error.original?.code] || {
        status: status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        message: error.message || QUICKBOOKS_MESSAGES.INTERNAL_SERVER_ERROR,
      }
    );
  } catch (err) {
    logger.error(QUICKBOOKS_ERROR_LOGS.HANDLE_DATABASE_ERROR_ERROR, {
      error: err.message,
      stack: err.stack,
      originalError: error,
    });
    return {
      status: status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      message: QUICKBOOKS_MESSAGES.INTERNAL_SERVER_ERROR,
    };
  }
};

/**
 * Generic error response handler
 * @param {Object} res - Express response object
 * @param {Error} error - Error object
 * @param {string} defaultMessage - Default error message
 * @returns {Object} HTTP response
 */
const sendErrorResponse = (
  res,
  error,
  _defaultMessage = QUICKBOOKS_MESSAGES.INTERNAL_SERVER_ERROR
) => {
  try {
    logger.error(QUICKBOOKS_ERROR_LOGS.CONTROLLER_ERROR, {
      error: error.message,
      stack: error.stack,
    });
    const errorInfo = handleDatabaseError(error);
    return res.status(errorInfo.status).json(errorResponse(errorInfo.message));
  } catch (err) {
    logger.error(QUICKBOOKS_ERROR_LOGS.SEND_ERROR_RESPONSE_ERROR, {
      error: err.message,
      stack: err.stack,
      originalError: error,
    });
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(QUICKBOOKS_MESSAGES.INTERNAL_SERVER_ERROR));
  }
};

export const addQuickbookAccount = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.ADDING_ACCOUNT);

  try {
    const { code, realmId, schemaName, email, organization_id } = req.query;

    // Handle organization realm_id operations through service layer
    if (realmId && organization_id) {
      const realmIdResult = await handleOrganizationRealmId(
        realmId,
        organization_id
      );

      if (!realmIdResult.success) {
        // If realm_id is already associated with another organization, return error
        if (realmIdResult.warning) {
          return res
            .status(status.STATUS_CODE_CONFLICT)
            .json(errorResponse(realmIdResult.message || realmIdResult.error));
        }
        // Log warning but don't fail the entire request for other errors
        logger.warn(
          `Realm ID operation completed with warnings: ${realmIdResult.error}`
        );
      }
    }

    // Validate required fields using helper
    const missingFields = validateRequiredFields(
      { code, organization_id },
      QUICKBOOKS_VALIDATION.REQUIRED_FIELDS.ADD_ACCOUNT
    );

    if (missingFields.length > 0) {
      const missingField = missingFields[0];
      const errorMessage =
        missingField === HARDCODED_STRINGS.CODE
          ? QUICKBOOKS_MESSAGES.AUTHORIZATION_CODE_NOT_FOUND
          : QUICKBOOKS_MESSAGES.ORGANIZATION_ID_REQUIRED;

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(errorMessage));
    }

    // Fetch organization schema name by realm_id

    // Exchange authorization code for access token
    const tokenData = await quickbooksService.exchangeAuthCodeForTokens(code);
    const { access_token, refresh_token } = tokenData;

    // Encrypt tokens securely
    const [encryptedAccessToken, encryptedRefreshToken] = await Promise.all([
      encrypt(access_token),
      encrypt(refresh_token),
    ]);

    // Check for existing account to prevent duplicates

    const accountData = {
      [QUICKBOOKS_FIELD_NAMES.ACCESS_TOKEN]: encryptedAccessToken,
      [QUICKBOOKS_FIELD_NAMES.REFRESH_TOKEN]: encryptedRefreshToken,
      [QUICKBOOKS_FIELD_NAMES.EMAIL]: email,
      [QUICKBOOKS_FIELD_NAMES.REALM_ID]: realmId,
      [QUICKBOOKS_FIELD_NAMES.UPDATED_AT]: new Date(),
    };

    await quickbooksRepository.create(accountData, schemaName);

    // Update QB connection status for the organization
    if (organization_id) {
      try {
        logger.info(
          `Updating QB connection status for organization: ${organization_id}`
        );
        const qbConnectionResult = await updateOrganizationQbConnection(
          organization_id
        );

        if (qbConnectionResult.success) {
          logger.info(
            `Successfully updated QB connection status for organization: ${organization_id}`
          );
        } else {
          logger.warn(
            `Failed to update QB connection status for organization ${organization_id}: ${qbConnectionResult.error}`
          );
          // Don't fail the entire request if QB connection update fails
        }
      } catch (qbConnectionError) {
        logger.warn(
          `Error updating QB connection status for organization ${organization_id}:`,
          {
            error: qbConnectionError.message,
          }
        );
        // Don't fail the entire request if QB connection update fails
      }
    }

    const accessToken = { access_token: access_token };

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(QUICKBOOKS_MESSAGES.ACCOUNT_AUTHORIZED, accessToken)
      );
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_ADDING_ACCOUNT, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

/**
 * Retrieves QuickBooks accounts with filtering and pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<Object>} HTTP response
 */
export const listQuickbookAccounts = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.GETTING_ACCOUNTS);

  try {
    const { active, organization_id } = req.query;

    // Build filter conditions using helper function
    const buildWhereClause = () => {
      const whereClause = {};

      if (organization_id) {
        whereClause[QUICKBOOKS_FIELD_NAMES.ORGANIZATION_ID] =
          parseInt(organization_id);
      } else if (req.user?.organization_id) {
        whereClause[QUICKBOOKS_FIELD_NAMES.ORGANIZATION_ID] =
          req.user.organization_id;
      } else if (req.user?.id) {
        whereClause[QUICKBOOKS_FIELD_NAMES.USER_ID] = req.user.id;
      }

      if (active !== undefined) {
        whereClause[QUICKBOOKS_FIELD_NAMES.STATUS] =
          active === HARDCODED_STRINGS.BOOLEAN.TRUE;
      }

      return whereClause;
    };

    const whereClause = buildWhereClause();
    const accounts = await quickbooksRepository.findWithUserInfo(whereClause);

    logger.info(QUICKBOOKS_CONTROLLER_LOGS.FOUND_ACCOUNTS(accounts.length));

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(QUICKBOOKS_MESSAGES.ACCOUNTS_FETCHED_SUCCESSFULLY, {
        accounts,
      })
    );
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_GETTING_ACCOUNTS, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

/**
 * Adds or updates QuickBooks tokens for an account
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<Object>} HTTP response
 */
export const addTokens = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.ADDING_TOKEN);

  try {
    const { id, refresh_token, access_token } = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(
      { id, refresh_token, access_token },
      QUICKBOOKS_VALIDATION.REQUIRED_FIELDS.ADD_TOKEN
    );

    if (missingFields.length > 0) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(QUICKBOOKS_MESSAGES.TOKENS_REQUIRED));
    }

    // Check if account exists
    const accountExists = await quickbooksRepository.exists(id);
    if (!accountExists) {
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND));
    }

    // Encrypt and update tokens
    const [encryptedRefreshToken, encryptedAccessToken] = await Promise.all([
      encrypt(refresh_token),
      encrypt(access_token),
    ]);

    const tokenData = {
      [QUICKBOOKS_FIELD_NAMES.REFRESH_TOKEN]: encryptedRefreshToken,
      [QUICKBOOKS_FIELD_NAMES.ACCESS_TOKEN]: encryptedAccessToken,
      [QUICKBOOKS_FIELD_NAMES.TOKEN_EXPIRY_TIME]: new Date(
        Date.now() + QUICKBOOKS_DEFAULTS.TOKEN_EXPIRY_MS
      ),
    };

    const updatedAccount = await quickbooksRepository.updateTokens(
      id,
      tokenData
    );

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(QUICKBOOKS_MESSAGES.TOKEN_ADDED_SUCCESSFULLY, {
        account: updatedAccount,
      })
    );
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_ADDING_TOKEN, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

/**
 * Refreshes QuickBooks access tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<Object>} HTTP response
 */
export const getTokens = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.GETTING_TOKENS);

  try {
    const { id } = req.body;

    // Validate required fields
    const missingFields = validateRequiredFields(
      { id },
      QUICKBOOKS_VALIDATION.REQUIRED_FIELDS.GET_TOKENS
    );
    if (missingFields.length > 0) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_ID_REQUIRED));
    }

    // Find account
    const quickbookAccount = await quickbooksRepository.findById(id);
    if (!quickbookAccount) {
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND));
    }

    // Refresh tokens using service
    const updatedAccount = await quickbooksService.refreshTokens(
      quickbookAccount
    );
    if (!updatedAccount) {
      return res
        .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
        .json(errorResponse(QUICKBOOKS_MESSAGES.FAILED_TO_REFRESH_TOKENS));
    }

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(QUICKBOOKS_MESSAGES.NEW_TOKEN_GENERATED));
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_ADDING_TOKEN, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

/**
 * Syncs QuickBooks data with local storage
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<Object>} HTTP response
 */
export const quickbooksFileSave = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.ADDING_FILE);

  const initiatedBy = req.body.isCronJob
    ? QUICKBOOKS_STATUS.AUTOMATIC
    : req.user?.email || HARDCODED_STRINGS.UNKNOWN_USER;

  try {
    const { organization_id, id, isCronJob } = req.body;

    // Handle sync all accounts scenario
    if (!organization_id || !id) {
      return await syncAllQuickBooksAccounts();
    }

    // Validate required fields for single account sync
    const missingFields = validateRequiredFields(
      { organization_id, id },
      QUICKBOOKS_VALIDATION.REQUIRED_FIELDS.SYNC_DATA
    );

    if (missingFields.length > 0) {
      await quickbooksService.logQuickBookSync(
        initiatedBy,
        QUICKBOOKS_STATUS.FAILED
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            HARDCODED_STRINGS.ORGANIZATION_ID_AND_ACCOUNT_ID_REQUIRED
          )
        );
    }

    // Find account
    const quickbookAccount = await quickbooksRepository.findById(id);
    if (!quickbookAccount) {
      await quickbooksService.logQuickBookSync(
        initiatedBy,
        QUICKBOOKS_STATUS.FAILED
      );
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND));
    }

    // Sync data using service
    const syncResult = await quickbooksService.syncAccountData(
      quickbookAccount,
      organization_id,
      initiatedBy
    );

    // Update last synced timestamp
    await quickbooksRepository.updateLastSynced(id);

    if (!isCronJob) {
      await quickbooksService.logQuickBookSync(
        initiatedBy,
        QUICKBOOKS_STATUS.COMPLETED
      );
    }

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(QUICKBOOKS_MESSAGES.DATA_SYNCED_SUCCESSFULLY, {
        uploadedFiles: syncResult,
      })
    );
  } catch (error) {
    await quickbooksService.logQuickBookSync(
      initiatedBy,
      QUICKBOOKS_STATUS.FAILED
    );
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_UPLOADING_FILE, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

/**
 * Updates QuickBooks account status (enable/disable)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<Object>} HTTP response
 */
export const statusDisable = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.DISABLING_ACCOUNT);

  try {
    const { accountStatus } = req.body;
    const { id } = req.params;

    // Find account
    const quickbookAccount = await quickbooksRepository.findById(id);
    if (!quickbookAccount) {
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND));
    }

    // Update status
    const updatedAccount = await quickbooksRepository.updateStatus(
      id,
      accountStatus
    );

    // Determine success message
    const message = accountStatus
      ? QUICKBOOKS_MESSAGES.ACCOUNT_ENABLED_SUCCESSFULLY
      : QUICKBOOKS_MESSAGES.ACCOUNT_DISABLED_SUCCESSFULLY;

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(message, { quickbookAccount: updatedAccount }));
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_DISABLING_ACCOUNT, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

/**
 * Syncs all QuickBooks accounts across all organizations
 * @returns {Promise<void>} Completion status
 */
export const syncAllQuickBooksAccounts = async () => {
  try {
    // Note: Organization model not available, would iterate through organizations
    const organizations = []; // await quickbooksRepository.getAllOrganizations();

    for (const org of organizations) {
      try {
        const quickbookAccounts =
          await quickbooksRepository.findByOrganizationId(org.id);

        const syncPromises = quickbookAccounts.map(async (account) => {
          try {
            return await quickbooksService.syncAccountData(
              account,
              org.id,
              QUICKBOOKS_STATUS.AUTOMATIC
            );
          } catch (error) {
            logger.error(QUICKBOOKS_ERROR_LOGS.SYNC_ACCOUNT_DATA_ERROR, {
              error: error.message,
              stack: error.stack,
              accountId: account.id,
              organizationId: org.id,
            });
            throw error;
          }
        });

        await Promise.allSettled(syncPromises);
      } catch (error) {
        logger.error(QUICKBOOKS_ERROR_LOGS.PROCESS_ORGANIZATION_ERROR, {
          error: error.message,
          stack: error.stack,
          organizationId: org.id,
        });
        throw error;
      }
    }
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.SYNC_ALL_ACCOUNTS_ERROR, {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

/**
 * Retrieves information about all Sequelize models
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<Object>} HTTP response
 */
export const getTables = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.GETTING_MODELS_INFO);

  try {
    // Call the service method to fetch all models info
    const modelsInfo = await quickbooksService.fetchAllModelsInfo();

    logger.info(
      `Successfully retrieved information for ${modelsInfo.totalModels} models`
    );

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(QUICKBOOKS_MESSAGES.MODELS_INFO_FETCHED_SUCCESSFULLY, {
        modelsInfo,
      })
    );
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_GETTING_MODELS_INFO, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

// Utility to run processReport and capture result without sending Express response
function runProcessReportWithBody(body, reportType) {
  let result;
  const fakeReq = { body };
  // We fake res.json().status(), storing the status in result
  const fakeRes = {
    _status: 200,
    status(s) {
      this._status = s;
      return this;
    },
    json(obj) {
      result = { status: this._status, ...obj };
    },
    send(obj) {
      result = { status: this._status, ...obj };
    },
  };
  return processReport(fakeReq, fakeRes, reportType).then(() => result);
}

// POST /sync-all-reports: process all key report types in sequence with the same req.body
export const syncAllReports = async (req, res) => {
  const { body } = req;
  // Reports to sync
  const reportTypes = [
    HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
    HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS,
    HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET,
    HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW,
  ];
  // Run all in parallel
  const results = await Promise.allSettled(
    reportTypes.map((type) => runProcessReportWithBody(body, type))
  );
  // Decorate response with reportType info
  const response = results.map((r, idx) => ({
    reportType: reportTypes[idx],
    ...("value" in r
      ? r.value
      : { success: false, error: r.reason?.message || "Unknown error" }),
  }));

  const organizationId = body.organization_id;

  // Update QuickBooks sync timestamp for the organization
  try {
    if (organizationId) {
      const syncApiUrl = `${ORG_API_BASE_URL}/sync/${organizationId}`;
      logger.info(
        `Updating QuickBooks sync timestamp for organization: ${organizationId}`
      );

      await axios.put(syncApiUrl, { type: "qb" });
      logger.info(
        `Successfully updated QuickBooks sync timestamp for organization: ${organizationId}`
      );
    }
  } catch (syncError) {
    logger.warn(
      `Failed to update QuickBooks sync timestamp for organization ${organizationId}:`,
      {
        error: syncError.message,
      }
    );
    // Don't fail the entire request if sync timestamp update fails
  }

  res.status(200).json({ reports: response });
};

// Export all controller functions
export default {
  addQuickbookAccount,
  listQuickbookAccounts,
  addTokens,
  getTokens,
  quickbooksFileSave,
  statusDisable,
  syncAllQuickBooksAccounts,
  getTables,
  syncAllReports,
};
