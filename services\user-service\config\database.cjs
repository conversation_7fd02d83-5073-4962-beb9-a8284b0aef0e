const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

/**
 * Parse environment variable value to appropriate type
 * @param {string} value - Environment variable value
 * @param {string} type - Target type ('string', 'number', 'boolean')
 * @param {*} defaultValue - Default value if parsing fails
 * @returns {*} Parsed value
 */
const parseEnvValue = (value, type, defaultValue) => {
  if (!value) return defaultValue;

  switch (type) {
    case "number":
      const num = parseInt(value, 10);
      return isNaN(num) ? defaultValue : num;
    case "boolean":
      return value.toLowerCase() === "true";
    default:
      return value;
  }
};

/**
 * Get database configuration based on environment
 * @returns {Object} Database configuration object
 */
const getDatabaseConfig = () => {
  // Check if using local database
  const isLocalDatabase =
    process.env.USE_LOCAL_DB === "true" ||
    process.env.NODE_ENV === "development";

  if (isLocalDatabase) {
    return {
      database:
        process.env.LOCAL_DB_NAME || process.env.DB_NAME || "cpa_dashboard",
      username: process.env.LOCAL_DB_USER || process.env.DB_USER || "postgres",
      password: process.env.LOCAL_DB_PASS || process.env.DB_PASS || "",
      host: process.env.LOCAL_DB_HOST || process.env.DB_HOST || "localhost",
      port: parseEnvValue(
        process.env.LOCAL_DB_PORT || process.env.DB_PORT,
        "number",
        5432
      ),
      dialect: "postgres",
      logging: process.env.NODE_ENV === "development" ? console.log : false,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000,
      },
      dialectOptions: {},
      isLocal: true,
    };
  } else {
    const dbSslEnabled =
      typeof process.env.DB_SSL !== "undefined"
        ? parseEnvValue(process.env.DB_SSL, "boolean", true)
        : true;

    return {
      database: process.env.DB_NAME || "cpa_dashboard",
      username: process.env.DB_USER || "postgres",
      password: process.env.DB_PASS || "",
      host: process.env.DB_HOST || "localhost",
      port: parseEnvValue(process.env.DB_PORT, "number", 5432),
      dialect: "postgres",
      logging: false,
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000,
      },
      dialectOptions: dbSslEnabled
        ? {
            ssl: {
              require: true,
              rejectUnauthorized: false,
            },
          }
        : {},
      isLocal: false,
    };
  }
};

// Get database configuration
const dbConfig = getDatabaseConfig();

module.exports = {
  development: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      schema: "Authentication",
    },
  },
  test: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: false,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      schema: "Authentication",
    },
  },
  production: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: false,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      schema: "Authentication",
    },
  },
};
