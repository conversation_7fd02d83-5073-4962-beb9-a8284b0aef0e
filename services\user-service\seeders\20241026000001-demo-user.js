"use strict";

import bcrypt from "bcryptjs";
import { v4 as uuidv4 } from "uuid";

export default {
  up: async (queryInterface, Sequelize) => {
    // Hash the password using bcrypt
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash("admin123", saltRounds);

    // Generate UUIDs for the demo data
    const userId = uuidv4();
    const organizationId = uuidv4(); // Using a random UUID for tenant_id

    // Insert a demo user based on user-service user.model.js structure
    await queryInterface.sequelize.query(`
      INSERT INTO "Authentication"."app_user" 
      ("id","full_name","email","phone_number","password_hash","organization_id","role","last_login","is_active","access_token","is_deleted","created_at","updated_at") 
      VALUES (
        '${userId}',
        'Admin User',
        '<EMAIL>',
        '******-0123',
        '${hashedPassword}',
        '${organizationId}',
        'admin',
        NULL,
        true,
        NULL,
        false,
        '${new Date().toISOString()}',
        '${new Date().toISOString()}'
      )
    `);

    console.log("✅ Demo user seeded successfully");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: password123");
    console.log("👤 Full Name: Demo User");
    console.log("📱 Phone: ******-0123");
    console.log("🎭 Role: admin");
  },

  down: async (queryInterface, Sequelize) => {
    // Remove the demo user
    await queryInterface.sequelize.query(`
      DELETE FROM "Authentication"."app_user" 
      WHERE email = '<EMAIL>'
    `);

    console.log("✅ Demo user removed successfully");
  },
};
